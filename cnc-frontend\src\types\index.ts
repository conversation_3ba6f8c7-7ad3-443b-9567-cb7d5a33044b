export interface CncInfor {
  Id: number;
  IP: string | null;
  Port: number | null;
  Name: string | null;
  Operator: string | null;
  MacAddress?: string | null; // Thêm MacAddress nếu backend có gửi
}

export interface CncLog {
  Id: number;
  IP: string | null;
  CncName: string | null;
  Status: string | null;
  UpdateAt: string; // ISO 8601 UTC date string (e.g., "2023-10-27T10:30:00.000Z")
  MotionTime: string | null;
}

export interface StopReason {
  Id: number;
  CncName: string | null;
  ReasonCode: number | null;
  StopTime: string | null; // ISO 8601 UTC date string
  ConfirmTime: string | null; // ISO 8601 UTC date string
}

export interface CncDowntimeNoOrder {
  id: number;
  cncId: number;
  downtimeDate: string; // YYYY-MM-DD format
  downtimeMinutes: number;
  notes: string | null;
  createdAt: string; // ISO 8601 UTC date string
  updatedAt: string; // ISO 8601 UTC date string
  createdBy: string | null;
  cncInfo?: {
    id: number;
    Name: string | null;
  };
}

// Kiểu dữ liệu cho message update từ WebSocket (đã dùng trong ví dụ Dashboard)
export interface CncWebSocketUpdate {
  id: number; // Tương ứng với CncInfor.Id
  name: string | null;
  status: string | null;
  motionTime: string | null;
  updateAt: string; // Thời điểm WebSocket server gửi bản tin này (ISO string)
}

// export interface CncInforFormData {
//   Name: string | null;
//   IP: string | null;
//   Port: number | null;
//   Operator: string | null;
//   MacAddress?: string | null;
// }


// Kiểu dữ liệu cho các form (ví dụ khi tạo/sửa CncInfor)
// Thường sẽ bỏ qua Id và một số trường readonly
export type CncInforFormData = Omit<CncInfor, 'Id'>;
export type StopReasonFormData = Omit<StopReason, 'Id' | 'ConfirmTime'>; // ConfirmTime thường do server đặt
export type CncDowntimeNoOrderFormData = Omit<CncDowntimeNoOrder, 'id' | 'createdAt' | 'updatedAt' | 'cncInfo'>;

// User Action Log types
export interface UserActionLog {
  id: number;
  userId: string;
  actionType: 'CREATE' | 'UPDATE' | 'DELETE';
  tableName: string;
  recordId: number;
  recordDescription: string;
  actionDetails?: string;
  actionDate: string;
}

export interface UserActionLogFormData {
  userId: string;
  actionType: 'CREATE' | 'UPDATE' | 'DELETE';
  tableName: string;
  recordId: number;
  recordDescription: string;
  actionDetails?: string;
}

export interface UserStats {
  userId: string;
  totalActions: number;
  createCount: number;
  updateCount: number;
  deleteCount: number;
  lastAction: string;
}

export interface TableStats {
  tableName: string;
  totalActions: number;
  createCount: number;
  updateCount: number;
  deleteCount: number;
  lastAction: string;
}

// Có thể thêm các kiểu dữ liệu khác nếu cần, ví dụ cho pagination, filters...