import { createUserActionLog } from '../services/apiService';
import type { UserActionLogFormData } from '../types';

export interface LogActionParams {
  userId: string;
  actionType: 'CREATE' | 'UPDATE' | 'DELETE';
  tableName: string;
  recordId: number;
  recordDescription: string;
  actionDetails?: string;
}

/**
 * Log user action to database
 */
export const logUserAction = async (params: LogActionParams): Promise<void> => {
  try {
    const logData: UserActionLogFormData = {
      userId: params.userId,
      actionType: params.actionType,
      tableName: params.tableName,
      recordId: params.recordId,
      recordDescription: params.recordDescription,
      actionDetails: params.actionDetails
    };

    await createUserActionLog(logData);
  } catch (error) {
    // Log error but don't throw to avoid breaking the main operation
    console.error('Failed to log user action:', error);
  }
};

/**
 * Helper function to create action description for CNC machines
 */
export const createCncActionDescription = (
  actionType: 'CREATE' | 'UPDATE' | 'DELETE',
  cncName: string,
  cncId: number
): string => {
  switch (actionType) {
    case 'CREATE':
      return `Tạo máy CNC mới: ${cncName} (ID: ${cncId})`;
    case 'UPDATE':
      return `Cập nhật máy CNC: ${cncName} (ID: ${cncId})`;
    case 'DELETE':
      return `Xóa máy CNC: ${cncName} (ID: ${cncId})`;
    default:
      return `Thao tác trên máy CNC: ${cncName} (ID: ${cncId})`;
  }
};

/**
 * Helper function to create action description for downtime records
 */
export const createDowntimeActionDescription = (
  actionType: 'CREATE' | 'UPDATE' | 'DELETE',
  cncName: string,
  downtimeDate: string,
  downtimeMinutes: number,
  recordId: number
): string => {
  const hours = Math.floor(downtimeMinutes / 60);
  const minutes = downtimeMinutes % 60;
  const timeStr = `${hours}h${minutes.toString().padStart(2, '0')}m`;
  
  switch (actionType) {
    case 'CREATE':
      return `Tạo bản ghi thời gian dừng: ${cncName} - ${downtimeDate} (${timeStr})`;
    case 'UPDATE':
      return `Cập nhật bản ghi thời gian dừng: ${cncName} - ${downtimeDate} (${timeStr})`;
    case 'DELETE':
      return `Xóa bản ghi thời gian dừng: ${cncName} - ${downtimeDate} (ID: ${recordId})`;
    default:
      return `Thao tác trên bản ghi thời gian dừng: ${cncName} - ${downtimeDate}`;
  }
};

/**
 * Helper function to create action description for OEE configuration
 */
export const createOeeConfigActionDescription = (
  actionType: 'UPDATE',
  configType: 'work_time' | 'break_time',
  oldValue?: string,
  newValue?: string
): string => {
  const configName = configType === 'work_time' ? 'thời gian làm việc' : 'thời gian nghỉ';
  
  if (oldValue && newValue) {
    return `Cập nhật cấu hình ${configName}: ${oldValue} → ${newValue}`;
  }
  
  return `Cập nhật cấu hình ${configName}`;
};

/**
 * Helper function to create action details JSON string
 */
export const createActionDetails = (data: Record<string, any>): string => {
  try {
    return JSON.stringify(data, null, 2);
  } catch (error) {
    console.error('Failed to stringify action details:', error);
    return JSON.stringify({ error: 'Failed to serialize data' });
  }
};
