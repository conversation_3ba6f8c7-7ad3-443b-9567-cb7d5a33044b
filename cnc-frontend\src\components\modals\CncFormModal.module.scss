// src/components/modals/CncFormModal.module.scss

:root {
  // Base Colors
  --color-white: #ffffff;
  --color-black: #000000;
  --color-gray-100: #f8f9fa7a;
  --color-gray-200: #e9ecef;
  --color-gray-300: #dee2e6;
  --color-gray-400: #ced4da;
  --color-gray-500: #adb5bd;
  --color-gray-600: #6c757d;
  --color-gray-700: #495057;
  --color-gray-800: #343a40;
  --color-gray-900: #212529;

  --color-blue-500: #007bff;
  --color-blue-600: #0069d9;
  --color-blue-700: #0056b3;
  --color-red-500: #dc3545;
  --color-red-600: #c82333;

  //Couple of gradient color 
  --color-start-1: #68A5E1;
  --color-end-1: #C777E9;

  // Semantic Colors (Light Theme - Default)
  --modal-overlay-bg: rgba(184, 184, 184, 0.212);
  --modal-content-bg: var(--color-gray-100);
  --modal-header-footer-bg: var(--color-gray-100);
  --modal-border-color: var(--color-gray-300);
  --modal-text-primary: var(--color-gray-900);
  --modal-text-secondary: var(--color-gray-700);
  --modal-input-bg: var(--color-white);
  --modal-input-border: var(--color-gray-400);
  --modal-input-focus-border: var(--color-blue-500);
  --modal-input-focus-shadow: rgba(0, 123, 255, 0.25);
  --modal-button-primary-bg: var(--color-blue-500);
  --modal-button-primary-hover-bg: var(--color-blue-600);
  --modal-button-secondary-bg: var(--color-gray-600);
  --modal-button-secondary-hover-bg: var(--color-gray-700);
  --modal-button-text: var(--color-white);
  --modal-error-text-color: var(--color-red-500);
  --modal-required-color: var(--color-red-500);
  --modal-error-input-border: var(--color-red-500);
  --modal-error-input-focus-shadow: rgba(220, 53, 69, 0.25);

  // Typography
  --modal-font-family-sans: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --modal-font-size-base: 1rem; // 16px
  --modal-font-size-lg: 1.25rem;
  --modal-font-size-sm: 0.875rem;
  --modal-font-weight-normal: 400;
  --modal-font-weight-medium: 500;
  --modal-font-weight-bold: 700;

  // Spacing
  --modal-spacing-xs: 0.25rem; // 4px
  --modal-spacing-sm: 0.5rem;  // 8px
  --modal-spacing-md: 1rem;    // 16px
  --modal-spacing-lg: 1.25rem; // 20px
  --modal-spacing-xl: 1.5rem;  // 24px

  // Borders & Shadows
  --modal-border-width: 1px;
  --modal-border-radius-sm: 0.25rem; // 4px
  --modal-border-radius-md: 0.5rem;  // 8px (was 8px, can make it slightly larger e.g., 0.75rem / 12px for more modern)
  --modal-border-radius-lg: 0.75rem; // 12px
  --modal-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15), 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);

  // Transitions
  --modal-transition-duration: 0.15s;
  --modal-transition-timing: ease-in-out;

  // To enable basic OS-level dark mode detection
  //color-scheme: light dark;
}

// Dark Mode Overrides
// @media (prefers-color-scheme: dark) {
//   :root {
//     --modal-overlay-bg: rgba(231, 231, 231, 0);
//     --modal-content-bg: var(--color-gray-800);
//     --modal-header-footer-bg: var(--color-gray-900);
//     --modal-border-color: var(--color-gray-700);
//     --modal-text-primary: var(--color-gray-100);
//     --modal-text-secondary: var(--color-gray-400);
//     --modal-input-bg: var(--color-gray-700);
//     --modal-input-border: var(--color-gray-600);
//     --modal-input-focus-border: var(--color-blue-500); // Keep focus bright
//     // --modal-input-focus-shadow: rgba(0, 123, 255, 0.35); // Slightly more visible shadow
//     --modal-button-primary-bg: var(--color-blue-600);
//     --modal-button-primary-hover-bg: var(--color-blue-500);
//     --modal-button-secondary-bg: var(--color-gray-600); // Or a slightly lighter gray if gray-700 is too dark
//     --modal-button-secondary-hover-bg: var(--color-gray-500);
//     --modal-button-text: var(--color-white); // Or var(--color-gray-100) for some buttons
//     --modal-error-text-color: #ff7887; // Lighter red for dark bg
//     --modal-required-color: #ff7887;
//     --modal-error-input-border: #ff7887;
//     // --modal-error-input-focus-shadow: rgba(255, 120, 135, 0.35);
//   }
// }


.modalOverlay {
  position: fixed;
  inset: 0; // Shorthand for top, right, bottom, left = 0
  background-color: var(--modal-overlay-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px); // Slightly increased blur for more depth
  padding: var(--modal-spacing-md); // Ensure content isn't flush with viewport edges
  font-family: var(--modal-font-family-sans);
}

.modalContent {
  background-color: var(--modal-content-bg);
  border-radius: var(--modal-border-radius-lg);
  box-shadow: var(--modal-box-shadow);
  width: 100%; // Take full width within padding of overlay
  max-width: 500px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: fadeInModal 0.4s var(--modal-transition-timing) forwards;
  overflow: hidden; // Ensure children with border-radius don't peek out
}

.modalContentHidden {
  animation: fadeOutModal 0.4s var(--modal-transition-timing) forwards;
  opacity: 0; // Ensure it's fully transparent when hidden
}

@keyframes fadeInModal {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
@keyframes fadeOutModal {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-20px) scale(0.98);
  }
}

.modalHeader {
  padding: var(--modal-spacing-md) var(--modal-spacing-lg);
  border-bottom: var(--modal-border-width) solid var(--modal-border-color);
  background-color: var(--modal-header-footer-bg);
  // border-top-left-radius: var(--modal-border-radius-lg); // Not needed due to .modalContent overflow:hidden
  // border-top-right-radius: var(--modal-border-radius-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0; // Prevent header from shrinking

  h2 {
    margin: 0;
    font-size: var(--modal-font-size-lg);
    font-weight: var(--modal-font-weight-medium); // Slightly less heavy than bold
    color: var(--modal-text-primary);
    line-height: 1.4;
  }
}

.closeButton {
  background: transparent;
  border: none;
  font-size: 1.75rem; // Slightly smaller if using an '×'
  font-weight: var(--modal-font-weight-normal);
  line-height: 1;
  color: var(--modal-text-secondary);
  cursor: pointer;
  padding: var(--modal-spacing-xs) var(--modal-spacing-sm);
  border-radius: var(--modal-border-radius-sm);
  transition: color var(--modal-transition-duration) var(--modal-transition-timing),
              background-color var(--modal-transition-duration) var(--modal-transition-timing);

  &:hover {
    color: var(--modal-text-primary);
    background-color: rgba(0,0,0,0.05); // Subtle hover for better affordance
  }
  &:focus-visible { // More accessible focus
    outline: 2px solid var(--modal-input-focus-border);
    outline-offset: 2px;
    color: var(--modal-text-primary);
  }
  @media (prefers-color-scheme: dark) {
    &:hover {
      background-color: rgba(255,255,255,0.1);
    }
  }
}

.modalBody {
  padding: var(--modal-spacing-lg);
  overflow-y: auto;
  flex-grow: 1;
  color: var(--modal-text-secondary); // Default text color for body
  font-size: var(--modal-font-size-base);
  line-height: 1.6;
}

.formGroup {
  margin-bottom: var(--modal-spacing-lg);

  label {
    display: block;
    margin-bottom: var(--modal-spacing-sm);
    font-weight: var(--modal-font-weight-medium);
    color: var(--modal-text-primary); // Labels are more primary
    font-size: var(--modal-font-size-base);

    .required {
      color: var(--modal-required-color);
      margin-left: var(--modal-spacing-xs);
      font-weight: var(--modal-font-weight-normal); // Make asterisk less bold
    }
  }

  input[type="text"],
  input[type="number"],
  input[type="date"],
  select,
  textarea {
    width: 100%;
    padding: var(--modal-spacing-sm) var(--modal-spacing-md);
    border: var(--modal-border-width) solid var(--modal-input-border);
    border-radius: var(--modal-border-radius-md);
    font-size: var(--modal-font-size-base);
    font-family: inherit; // Ensure it uses the modal's font
    background-color: var(--modal-input-bg);
    color: var(--modal-text-primary);
    box-sizing: border-box;
    transition: border-color var(--modal-transition-duration) var(--modal-transition-timing),
                box-shadow var(--modal-transition-duration) var(--modal-transition-timing);

    &::placeholder {
      color: var(--color-gray-500);
      opacity: 1; // Firefox
    }

    &:focus, &:focus-visible { // Combine for consistency, focus-visible for keyboard
      border-color: var(--modal-input-focus-border);
      outline: 0;
      box-shadow: 0 0 0 0.2rem var(--modal-input-focus-shadow);
    }
  }

  textarea {
    min-height: 80px; // Give textarea a decent default height
    resize: vertical; // Allow vertical resize, not horizontal
  }

  .inputError { // Class applied to the input itself
    border-color: var(--modal-error-input-border) !important; // Override base border
    &:focus, &:focus-visible {
      box-shadow: 0 0 0 0.2rem var(--modal-error-input-focus-shadow) !important;
    }
  }

  .errorMessage {
    display: block; // Ensure it takes its own line
    color: var(--modal-error-text-color);
    font-size: var(--modal-font-size-sm);
    margin-top: var(--modal-spacing-sm);
  }
}

.modalFooter {
  padding: var(--modal-spacing-md) var(--modal-spacing-lg);
  border-top: var(--modal-border-width) solid var(--modal-border-color);
  background-color: var(--modal-header-footer-bg);
  // border-bottom-left-radius: var(--modal-border-radius-lg); // Not needed
  // border-bottom-right-radius: var(--modal-border-radius-lg);
  display: flex;
  justify-content: flex-end;
  gap: var(--modal-spacing-md);
  flex-shrink: 0; // Prevent footer from shrinking

  button {
    padding: var(--modal-spacing-sm) var(--modal-spacing-lg);
    border: none;
    border-radius: var(--modal-border-radius-md);
    font-size: var(--modal-font-size-base);
    font-weight: var(--modal-font-weight-medium);
    cursor: pointer;
    transition: background-color var(--modal-transition-duration) var(--modal-transition-timing),
                opacity var(--modal-transition-duration) var(--modal-transition-timing);
    color: var(--modal-button-text);
    line-height: 1.5;

    &:focus-visible {
        outline: 2px solid var(--modal-input-focus-border);
        outline-offset: 2px;
    }

    &:disabled {
      opacity: 0.65;
      cursor: not-allowed;
    }
  }
}

// .cancelButton {
//   padding: 10px 20px;
//   border: 1px solid #ccc;
//   background-color: transparent;
//   color: #333;
//   border-radius: 8px;
//   font-size: 16px;
//   cursor: pointer;
//   transition: all 0.25s ease-in-out;
//   &:hover {
//     background-color: rgba(0, 0, 0, 0.05);
//     border-color: #999;
//   }
// }

.submitButton {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--color-start-1), var(--color-end-1));
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -75%;
    width: 50%; 
    height: 100%;
    background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.6) 50%,
      rgba(255, 255, 255, 0.2) 100%
    );
    transform: skewX(-25deg);
  }
  &:hover:not(:disabled) {
    &::before {
      animation: shine 0.8s forwards;
    }
  }
}

@keyframes shine {
  to {
    left: 125%;
  }
}