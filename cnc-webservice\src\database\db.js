const sqlite3 = require('sqlite3').verbose();
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') }); // Load .env from root

const dbPath = process.env.DATABASE_PATH || path.join(__dirname, '../../data/cncdata.db'); // Default path if not in .env

// Ensure the directory exists (optional, sqlite3 creates the file but not the directory)
const dbDir = path.dirname(dbPath);
const fs = require('fs');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error opening database', err.message);
  } else {
    console.log(`Connected to the SQLite database at ${dbPath}`);

    // B<PERSON>t chế độ WAL để tăng cường xử lý đồng thời
    db.run('PRAGMA journal_mode = WAL;', (pragmaErr) => {
      if (pragmaErr) {
        console.error('Failed to enable WAL mode:', pragmaErr.message);
      } else {
        console.log('Write-Ahead Logging (WAL) mode enabled.');
      }
    });

    // Initialize tables
    db.serialize(() => {
      db.run(`CREATE TABLE IF NOT EXISTS CncInfor (
        Id INTEGER PRIMARY KEY AUTOINCREMENT,
        IP TEXT,
        Port INTEGER,
        Name TEXT,
        Operator TEXT
      )`);
      db.run(`CREATE TABLE IF NOT EXISTS CncLogs (
        Id INTEGER PRIMARY KEY AUTOINCREMENT,
        IP TEXT,
        CncName TEXT,
        Status TEXT,
        UpdateAt VARCHAR(50),
        MotionTime TEXT
      )`);
      db.run(`CREATE TABLE IF NOT EXISTS StopReasons (
        Id INTEGER PRIMARY KEY AUTOINCREMENT,
        CncName TEXT,
        ReasonCode INTEGER,
        StopTime TEXT,
        ConfirmTime TEXT
      )`);

      // Create CncDowntimeNoOrder table
      db.run(`CREATE TABLE IF NOT EXISTS CncDowntimeNoOrder (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cncId INTEGER NOT NULL,
        downtimeDate DATE NOT NULL,
        downtimeMinutes REAL NOT NULL,
        notes TEXT,
        createdAt DATETIME NOT NULL DEFAULT (datetime('now')),
        updatedAt DATETIME NOT NULL DEFAULT (datetime('now')),
        createdBy TEXT,
        FOREIGN KEY (cncId) REFERENCES CncInfor(Id) ON DELETE CASCADE,
        UNIQUE (cncId, downtimeDate)
      )`);

      console.log('Database tables checked/created.');
    });
  }
});

module.exports = db; 