/* src/pages/UserActionLogPage.module.scss */
.pageContainer {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: nowrap;
  gap: 20px;
  
  h1 {
    margin: 0;
    color: var(--text-primary-color, #333);
    white-space: nowrap;
    font-size: 1.8rem;
    flex-shrink: 0;
  }
}

.viewModeButtons {
  display: flex;
  gap: 10px;
  
  button {
    padding: 8px 16px;
    border: 2px solid var(--button-primary-bg, #007bff);
    background-color: transparent;
    color: var(--button-primary-bg, #007bff);
    border-radius: var(--border-radius-sm, 4px);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--button-primary-bg, #007bff);
      color: var(--button-text-color, #fff);
    }

    &.active {
      background-color: var(--button-primary-bg, #007bff);
      color: var(--button-text-color, #fff);
    }
  }
}

.filtersContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--card-bg-color, #fff);
  border-radius: var(--border-radius-md, 8px);
  box-shadow: var(--card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1));

  div {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary-color, #555);
  }

  input[type="date"],
  input[type="text"],
  select {
    padding: 8px 10px;
    border: 1px solid var(--input-border-color, #ccc);
    border-radius: var(--border-radius-sm, 4px);
    font-size: 0.95rem;
    min-width: 150px;
  }

  button {
    padding: 10px 20px;
    background-color: var(--button-secondary-bg, #6c757d);
    color: var(--button-text-color, #fff);
    border: none;
    border-radius: var(--border-radius-sm, 4px);
    cursor: pointer;
    font-weight: 500;
    align-self: flex-end;

    &:hover {
      background-color: var(--button-secondary-hover-bg, #545b62);
    }
  }
}

.error {
  padding: 10px;
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: var(--border-radius-sm, 4px);
  color: #dc3545;
  margin-bottom: 20px;
}

.tableContainer {
  background-color: var(--card-bg-color, #fff);
  border-radius: var(--border-radius-md, 8px);
  box-shadow: var(--card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1));
  overflow: hidden;
}

.loading {
  text-align: center;
  padding: 40px;
  color: var(--text-secondary-color, #555);
  font-size: 1.1rem;
}

.table {
  width: 100%;
  border-collapse: collapse;

  th,
  td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color, #dee2e6);
  }

  th {
    background-color: var(--table-header-bg, #f8f9fa);
    font-weight: 600;
    color: var(--text-primary-color, #333);
  }

  tbody tr:hover {
    background-color: var(--table-row-hover-bg, #f5f5f5);
  }
}

.userBadge {
  display: inline-block;
  padding: 4px 8px;
  background-color: rgba(0, 123, 255, 0.1);
  color: #007bff;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
}

.actionBadge {
  display: inline-block;
  padding: 4px 8px;
  color: white;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
}

.details {
  summary {
    cursor: pointer;
    color: var(--button-primary-bg, #007bff);
    font-size: 0.85rem;
    
    &:hover {
      text-decoration: underline;
    }
  }

  pre {
    margin-top: 8px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: pre-wrap;
    word-break: break-word;
    max-width: 300px;
    overflow-x: auto;
  }
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: var(--table-footer-bg, #f8f9fa);

  span {
    color: var(--text-secondary-color, #555);
    font-size: 0.9rem;
  }

  div {
    display: flex;
    gap: 10px;
  }

  button {
    padding: 8px 15px;
    background-color: var(--button-secondary-bg, #6c757d);
    color: var(--button-text-color, #fff);
    border: none;
    border-radius: var(--border-radius-sm, 4px);
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;

    &:hover:not(:disabled) {
      background-color: var(--button-secondary-hover-bg, #545b62);
    }

    &:disabled {
      background-color: var(--button-disabled-bg, #ccc);
      cursor: not-allowed;
    }
  }
}

.statsContainer {
  h2 {
    margin-bottom: 20px;
    color: var(--text-primary-color, #333);
  }
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.statCard {
  background-color: var(--card-bg-color, #fff);
  border-radius: var(--border-radius-md, 8px);
  box-shadow: var(--card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1));
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  h3 {
    margin: 0 0 15px 0;
    color: var(--text-primary-color, #333);
    font-size: 1.2rem;
    border-bottom: 2px solid var(--button-primary-bg, #007bff);
    padding-bottom: 8px;
  }
}

.statNumbers {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;

  div {
    padding: 8px;
    background-color: var(--table-header-bg, #f8f9fa);
    border-radius: var(--border-radius-sm, 4px);
    text-align: center;
    font-size: 0.9rem;

    strong {
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
}

.lastAction {
  font-size: 0.85rem;
  color: var(--text-secondary-color, #555);
  text-align: center;
  padding: 8px;
  background-color: rgba(0, 123, 255, 0.05);
  border-radius: var(--border-radius-sm, 4px);
}
