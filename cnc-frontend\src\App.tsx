import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import Header from './components/layout/Header';
import Sidebar from './components/layout/Sidebar';
import Footer from './components/layout/Footer';
import DashboardPage from './pages/DashboardPage';
import ManagementPage from './pages/ManagementPage';
import CncLogsPage from './pages/CncLogsPage';
import StopReasonPage from './pages/StopReasonPage';
import OEEPage from './pages/OEEPage';
import DowntimeNoOrderPage from './pages/DowntimeNoOrderPage';
import UserActionLogPage from './pages/UserActionLogPage';
import styles from './components/layout/Layout.module.scss';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './App.css'; // Global styles (nếu có)

// Hàm hoặc component con để lấy title dựa trên location
const DynamicHeader: React.FC = () => {
  const location = useLocation();
  let pageTitle = "OEE System"; // Default title

  switch (location.pathname) {
    case '/':
      pageTitle = "Performance Dashboard";
      break;
    case '/management':
      pageTitle = "Quản lý máy";
      break;
    case '/logs':
      pageTitle = "Quản lý Logs";
      break;
    case '/stopreason':
      pageTitle = "Quản lý Lý do Dừng Máy";
      break;
    case '/oee': // Thêm case cho trang OEE
       pageTitle = "Phân tích OEE";
      break;
    case '/downtime-no-order':
      pageTitle = "Quản lý thời gian dừng không có đơn hàng";
      break;
    case '/user-action-logs':
      pageTitle = "Lịch sử thao tác người dùng";
      break;
    // Thêm các case khác nếu cần
  }

  return <Header pageTitle={pageTitle} className={`${styles.header} ${styles['liquid-border']}`.trim()} />;
};


const App: React.FC = () => {
  const [isSidebarHovered, setIsSidebarHovered] = useState(false);

    // Hàm helper để hiển thị toast (tùy chọn, có thể gọi toast.success/error trực tiếp)
  // const showToast = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
  //   toast[type](message, {
  //     position: "top-right",
  //     autoClose: 3000, // Đóng sau 3 giây
  //     hideProgressBar: false,
  //     closeOnClick: true,
  //     pauseOnHover: true,
  //     draggable: true,
  //     progress: undefined,
  //     theme: "colored", // Hoặc "light", "dark"
  //   });
  // };


  return (
    <Router>
      <div className={styles.appContainer}>
        <div className="mica-background">
          <div className="mica-overlay"></div>
          <div className="floating-orb orb1"></div>
          <div className="floating-orb orb2"></div>
          <div className="floating-orb orb3"></div>
          <div className="floating-orb orb4"></div>
        </div>
        <Sidebar
          setIsHovered={setIsSidebarHovered}
          expandedClass={isSidebarHovered ? styles.sidebarExpanded : styles.sidebarCollapsed}
        />
        {/* Wrapper for content that will be pushed by sidebar */}
        <div
          className={styles.contentWrapper}
        >
          {/* Div này là sticky container và có width 100% */}
          {/* Toast Container: Đặt ở đây để nó hiển thị trên toàn bộ ứng dụng */}
            <ToastContainer
              position="top-right"
              autoClose={3000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
              theme="colored" />
          <div className={styles.headerContainerSticky}>
            <DynamicHeader />
          </div>
          <main className={styles.pageContent}>
            <Routes>
              <Route path="/" element={<DashboardPage />} />
              <Route path="/oee" element={<OEEPage />} />
              <Route path="/management" element={<ManagementPage />} />
              <Route path="/logs" element={<CncLogsPage />} />
              <Route path="/stopreason" element={<StopReasonPage />} />
              <Route path="/downtime-no-order" element={<DowntimeNoOrderPage />} />
              <Route path="/user-action-logs" element={<UserActionLogPage />} />
              {/* Thêm các routes khác ở đây nếu cần */}
            </Routes>
          </main>
          <Footer />
        </div>
      </div>
    </Router>
  );
};

export default App;
