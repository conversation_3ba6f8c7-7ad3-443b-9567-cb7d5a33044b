import React, { useState, useEffect } from 'react';
import type { FormEvent } from 'react';
import styles from './CncFormModal.module.scss'; // Sử dụng chung CSS với CncFormModal
import type { CncInfor, CncDowntimeNoOrder, CncDowntimeNoOrderFormData } from '../../types';
import {
  getMinSelectableDate,
  getMaxSelectableDate,
  validateAndAdjustDate
} from '../../utils/dateUtils';

interface DowntimeFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CncDowntimeNoOrderFormData, id?: number) => void;
  initialData?: CncDowntimeNoOrder | null;
  isEditMode: boolean;
  isLoading: boolean;
  cncList: CncInfor[];
}

const DowntimeFormModal: React.FC<DowntimeFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  isEditMode,
  isLoading,
  cncList
}) => {
  const [formData, setFormData] = useState<CncDowntimeNoOrderFormData>({
    cncId: 0,
    downtimeDate: getMaxSelectableDate(),
    downtimeMinutes: 0,
    notes: '',
    createdBy: 'User'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen) {
      if (isEditMode && initialData) {
        setFormData({
          cncId: initialData.cncId,
          downtimeDate: initialData.downtimeDate,
          downtimeMinutes: initialData.downtimeMinutes,
          notes: initialData.notes || '',
          createdBy: initialData.createdBy || 'User'
        });
      } else {
        // Reset form cho chế độ "Add New"
        setFormData({
          cncId: 0,
          downtimeDate: getMaxSelectableDate(),
          downtimeMinutes: 0,
          notes: '',
          createdBy: 'User'
        });
      }
      setErrors({}); // Reset errors khi modal mở
    }
  }, [isOpen, isEditMode, initialData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    let processedValue: any = value;

    if (name === 'cncId') {
      processedValue = parseInt(value, 10);
    } else if (name === 'downtimeMinutes') {
      processedValue = parseFloat(value) || 0;
    } else if (name === 'downtimeDate') {
      processedValue = validateAndAdjustDate(value);
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue,
    }));

    // Xóa lỗi khi người dùng bắt đầu nhập
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.cncId || formData.cncId === 0) {
      newErrors.cncId = 'Vui lòng chọn máy CNC.';
    }

    if (!formData.downtimeDate) {
      newErrors.downtimeDate = 'Vui lòng chọn ngày.';
    }

    if (formData.downtimeMinutes < 0 || formData.downtimeMinutes > 1440) {
      newErrors.downtimeMinutes = 'Thời gian dừng phải từ 0 đến 1440 phút (24 giờ).';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData, isEditMode ? initialData?.id : undefined);
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={isOpen ? styles.modalContent : styles.modalContentHidden} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h2>{isEditMode ? 'Chỉnh sửa thời gian dừng' : 'Thêm thời gian dừng mới'}</h2>
          <button onClick={onClose} className={styles.closeButton} aria-label="Đóng modal">×</button>
        </div>
        <form onSubmit={handleSubmit} className={styles.modalBody}>
          <div className={styles.formGroup}>
            <label htmlFor="cncId">Máy CNC <span className={styles.required}>*</span></label>
            <select
              id="cncId"
              name="cncId"
              value={formData.cncId}
              onChange={handleChange}
              className={errors.cncId ? styles.inputError : ''}
            >
              <option value={0}>Chọn máy CNC</option>
              {cncList.map(cnc => (
                <option key={cnc.Id} value={cnc.Id}>{cnc.Name}</option>
              ))}
            </select>
            {errors.cncId && <p className={styles.errorMessage}>{errors.cncId}</p>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="downtimeDate">Ngày <span className={styles.required}>*</span></label>
            <input
              type="date"
              id="downtimeDate"
              name="downtimeDate"
              value={formData.downtimeDate}
              onChange={handleChange}
              min={getMinSelectableDate()}
              max={getMaxSelectableDate()}
              className={errors.downtimeDate ? styles.inputError : ''}
            />
            {errors.downtimeDate && <p className={styles.errorMessage}>{errors.downtimeDate}</p>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="downtimeMinutes">Thời gian dừng (phút) <span className={styles.required}>*</span></label>
            <input
              type="number"
              id="downtimeMinutes"
              name="downtimeMinutes"
              value={formData.downtimeMinutes}
              onChange={handleChange}
              min={0}
              max={1440}
              placeholder="Ví dụ: 120"
              className={errors.downtimeMinutes ? styles.inputError : ''}
            />
            <small style={{ color: 'var(--modal-text-secondary)', fontSize: '0.875rem', marginTop: '0.25rem', display: 'block' }}>
              Tối đa 1440 phút (24 giờ)
            </small>
            {errors.downtimeMinutes && <p className={styles.errorMessage}>{errors.downtimeMinutes}</p>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="notes">Ghi chú</label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes || ''}
              onChange={handleChange}
              placeholder="Ghi chú về lý do dừng máy..."
              rows={3}
            />
          </div>

          <div className={styles.modalFooter}>
            <button type="submit" className={styles.submitButton} disabled={isLoading}>
              {isLoading ? (isEditMode ? 'Đang cập nhật...' : 'Đang tạo...') : (isEditMode ? 'Lưu thay đổi' : 'Tạo mới')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DowntimeFormModal;
