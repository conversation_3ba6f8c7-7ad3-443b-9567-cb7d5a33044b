import apiClient from './api';
import type { <PERSON>nc<PERSON>nfor, CncLog, StopReason, CncDowntimeNoOrder, CncInforFormData, StopReasonFormData, CncDowntimeNoOrderFormData } from '../types';

interface PaginatedCncLogsResponse {
  logs: CncLog[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  status?: string;
}

interface PaginatedStopReasonsResponse {
  reasons: StopReason[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  reasonCode?: number | string;
}

interface PaginatedDowntimeNoOrderResponse {
  success: boolean;
  data: CncDowntimeNoOrder[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// === CncInfor API ===
export const getAllCncInfor = async (): Promise<CncInfor[]> => {
  const response = await apiClient.get<CncInfor[]>('/cnc-infor');
  return response.data;
};

export const getCncInforById = async (id: number): Promise<CncInfor> => {
  const response = await apiClient.get<CncInfor>(`/cnc-infor/${id}`);
  return response.data;
};

export const createCncInfor = async (data: CncInforFormData): Promise<{ message: string, id: number }> => {
  const response = await apiClient.post<{ message: string, id: number }>('/cnc-infor', data);
  return response.data;
};

export const updateCncInfor = async (id: number, data: Partial<CncInforFormData>): Promise<{ message: string }> => {
  const response = await apiClient.put<{ message: string }>(`/cnc-infor/${id}`, data);
  return response.data;
};

export const deleteCncInfor = async (id: number): Promise<{ message: string }> => {
  const response = await apiClient.delete<{ message: string }>(`/cnc-infor/${id}`);
  return response.data;
};

// === CncLogs API ===
interface GetCncLogsParams {
  cncName?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  status?: string;
}

export const getCncLogs = async (params?: GetCncLogsParams): Promise<PaginatedCncLogsResponse> => {
  const response = await apiClient.get<PaginatedCncLogsResponse>('/cnc-logs', { params });
  return response.data;
};

export const getCncLogById = async (id: number): Promise<CncLog> => {
  const response = await apiClient.get<CncLog>(`/cnc-logs/${id}`);
  return response.data;
};

export const deleteCncLog = async (id: number): Promise<{ message: string }> => {
  const response = await apiClient.delete<{ message: string }>(`/cnc-logs/${id}`);
  return response.data;
};

export const deleteOldCncLogs = async (days?: number): Promise<{ message: string }> => {
  const response = await apiClient.delete<{ message: string }>('/cnc-logs/old', { params: { days } });
  return response.data;
};

export const deleteCncLogsByStatus = async (status: string): Promise<{ message: string }> => {
    const response = await apiClient.delete<{ message: string }>('/cnc-logs/status', { params: { status } });
    return response.data;
};

export const getUniqueCncLogStatuses = async (): Promise<string[]> => {
  const response = await apiClient.get<string[]>('/cnc-logs/statuses');
  return response.data;
};

// === StopReasons API ===
interface GetStopReasonsParams {
  cncName?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number; 
}

export const getStopReasons = async (params?: GetStopReasonsParams): Promise<PaginatedStopReasonsResponse> => {
  const response = await apiClient.get<PaginatedStopReasonsResponse>('/stop-reasons', { params });
  return response.data;
};

export const getUnconfirmedStopReasons = async (cncName?: string): Promise<StopReason[]> => {
  const response = await apiClient.get<StopReason[]>('/stop-reasons/unconfirmed', { params: { cncName } });
  return response.data;
};

export const getStopReasonById = async (id: number): Promise<StopReason> => {
  const response = await apiClient.get<StopReason>(`/stop-reasons/${id}`);
  return response.data;
};

export const createStopReason = async (data: StopReasonFormData): Promise<{ message: string, id: number }> => {
  const response = await apiClient.post<{ message: string, id: number }>('/stop-reasons', data);
  return response.data;
};

export const updateStopReason = async (id: number, data: Partial<StopReasonFormData>): Promise<{ message: string }> => {
  const response = await apiClient.put<{ message: string }>(`/stop-reasons/${id}`, data);
  return response.data;
};

export const confirmStopReason = async (id: number, confirmTime?: string): Promise<{ message: string }> => {
  const response = await apiClient.patch<{ message: string }>(`/stop-reasons/${id}/confirm`, { ConfirmTime: confirmTime });
  return response.data;
};

export const deleteStopReason = async (id: number): Promise<{ message: string }> => {
  const response = await apiClient.delete<{ message: string }>(`/stop-reasons/${id}`);
  return response.data;
};

// === CncDowntimeNoOrder API ===
interface GetDowntimeNoOrderParams {
  cncId?: number;
  downtimeDate?: string;
  startDate?: string;
  endDate?: string;
  periodType?: 'day' | 'week' | 'month';
  page?: number;
  limit?: number;
}

export const getDowntimeNoOrders = async (params?: GetDowntimeNoOrderParams): Promise<PaginatedDowntimeNoOrderResponse> => {
  const response = await apiClient.get<PaginatedDowntimeNoOrderResponse>('/cnc-downtime-no-order', { params });
  return response.data;
};

export const createDowntimeNoOrder = async (data: CncDowntimeNoOrderFormData): Promise<{ success: boolean; message: string; data: CncDowntimeNoOrder }> => {
  const response = await apiClient.post<{ success: boolean; message: string; data: CncDowntimeNoOrder }>('/cnc-downtime-no-order', data);
  return response.data;
};

export const updateDowntimeNoOrder = async (id: number, data: Partial<CncDowntimeNoOrderFormData>): Promise<{ success: boolean; message: string; data: CncDowntimeNoOrder }> => {
  const response = await apiClient.put<{ success: boolean; message: string; data: CncDowntimeNoOrder }>(`/cnc-downtime-no-order/${id}`, data);
  return response.data;
};

export const deleteDowntimeNoOrder = async (id: number): Promise<{ success: boolean; message: string }> => {
  const response = await apiClient.delete<{ success: boolean; message: string }>(`/cnc-downtime-no-order/${id}`);
  return response.data;
};

// User Action Log API functions
interface GetUserActionLogsParams {
  userId?: string;
  actionType?: string;
  tableName?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

interface PaginatedUserActionLogResponse {
  success: boolean;
  data: UserActionLog[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export const getUserActionLogs = async (params?: GetUserActionLogsParams): Promise<PaginatedUserActionLogResponse> => {
  const response = await apiClient.get<PaginatedUserActionLogResponse>('/user-action-logs', { params });
  return response.data;
};

export const createUserActionLog = async (data: UserActionLogFormData): Promise<{ success: boolean; message: string; data: UserActionLog }> => {
  const response = await apiClient.post<{ success: boolean; message: string; data: UserActionLog }>('/user-action-logs', data);
  return response.data;
};

export const deleteUserActionLog = async (id: number): Promise<{ success: boolean; message: string }> => {
  const response = await apiClient.delete<{ success: boolean; message: string }>(`/user-action-logs/${id}`);
  return response.data;
};

export const getUserStats = async (): Promise<{ success: boolean; data: UserStats[] }> => {
  const response = await apiClient.get<{ success: boolean; data: UserStats[] }>('/user-action-logs/stats/users');
  return response.data;
};

export const getTableStats = async (): Promise<{ success: boolean; data: TableStats[] }> => {
  const response = await apiClient.get<{ success: boolean; data: TableStats[] }>('/user-action-logs/stats/tables');
  return response.data;
};