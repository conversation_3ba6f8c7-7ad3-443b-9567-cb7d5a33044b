const db = require('../database/db');

const CncDowntimeNoOrder = {
  // Tạo bảng nếu chưa tồn tại
  createTable: () => {
    return new Promise((resolve, reject) => {
      const sql = `CREATE TABLE IF NOT EXISTS CncDowntimeNoOrder (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cncId INTEGER NOT NULL,
        downtimeDate DATE NOT NULL,
        downtimeMinutes REAL NOT NULL,
        notes TEXT,
        createdAt DATETIME NOT NULL DEFAULT (datetime('now')),
        updatedAt DATETIME NOT NULL DEFAULT (datetime('now')),
        createdBy TEXT,
        FOREIGN KEY (cncId) REFERENCES CncInfor(Id) ON DELETE CASCADE,
        UNIQUE (cncId, downtimeDate)
      )`;

      db.run(sql, (err) => {
        if (err) {
          console.error('Error creating CncDowntimeNoOrder table:', err);
          reject(err);
        } else {
          console.log('CncDowntimeNoOrder table checked/created.');
          resolve();
        }
      });
    });
  },

  // Tạo bản ghi mới
  create: (data, callback) => {
    const { cncId, downtimeDate, downtimeMinutes, notes, createdBy } = data;
    const sql = `INSERT INTO CncDowntimeNoOrder (cncId, downtimeDate, downtimeMinutes, notes, createdBy)
                 VALUES (?, ?, ?, ?, ?)`;

    db.run(sql, [cncId, downtimeDate, downtimeMinutes, notes, createdBy], function (err) {
      if (err) {
        callback(err, null);
      } else {
        // Lấy bản ghi vừa tạo với thông tin CNC
        CncDowntimeNoOrder.getById(this.lastID, callback);
      }
    });
  },

  // Lấy tất cả bản ghi với pagination và filter
  getAll: (params, callback) => {
    const { cncId, downtimeDate, startDate, endDate, periodType, page = 1, limit = 50 } = params;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let whereParams = [];
    let conditions = [];

    // Filter by cncId
    if (cncId) {
      conditions.push('d.cncId = ?');
      whereParams.push(cncId);
    }

    // Filter by date range
    if (startDate && endDate) {
      conditions.push('d.downtimeDate BETWEEN ? AND ?');
      whereParams.push(startDate, endDate);
    } else if (downtimeDate) {
      conditions.push('d.downtimeDate = ?');
      whereParams.push(downtimeDate);
    }

    // Period type filtering
    if (periodType && !downtimeDate && !startDate && !endDate) {
      const now = new Date();
      let dateFilter;

      switch (periodType) {
        case 'week':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - 7);
          conditions.push('d.downtimeDate >= ?');
          whereParams.push(weekStart.toISOString().split('T')[0]);
          break;
        case 'month':
          const monthStart = new Date(now);
          monthStart.setDate(now.getDate() - 30);
          conditions.push('d.downtimeDate >= ?');
          whereParams.push(monthStart.toISOString().split('T')[0]);
          break;
        case 'day':
        default:
          conditions.push('d.downtimeDate = ?');
          whereParams.push(now.toISOString().split('T')[0]);
          break;
      }
    }

    if (conditions.length > 0) {
      whereClause = 'WHERE ' + conditions.join(' AND ');
    }

    // Count total records
    const countSql = `SELECT COUNT(*) as total
                      FROM CncDowntimeNoOrder d
                      LEFT JOIN CncInfor c ON d.cncId = c.Id
                      ${whereClause}`;

    db.get(countSql, whereParams, (err, countResult) => {
      if (err) {
        callback(err, null);
        return;
      }

      const total = countResult.total;
      const totalPages = Math.ceil(total / limit);

      // Get paginated data
      const dataSql = `SELECT d.*, c.Name as cncName
                       FROM CncDowntimeNoOrder d
                       LEFT JOIN CncInfor c ON d.cncId = c.Id
                       ${whereClause}
                       ORDER BY d.downtimeDate DESC, d.createdAt DESC
                       LIMIT ? OFFSET ?`;

      db.all(dataSql, [...whereParams, limit, offset], (err, rows) => {
        if (err) {
          callback(err, null);
        } else {
          // Transform data to match frontend expectations
          const transformedRows = rows.map(row => ({
            id: row.id,
            cncId: row.cncId,
            downtimeDate: row.downtimeDate,
            downtimeMinutes: row.downtimeMinutes,
            notes: row.notes,
            createdAt: row.createdAt,
            updatedAt: row.updatedAt,
            createdBy: row.createdBy,
            cncInfo: {
              id: row.cncId,
              Name: row.cncName
            }
          }));

          callback(null, {
            data: transformedRows,
            pagination: {
              total,
              page: parseInt(page),
              limit: parseInt(limit),
              totalPages
            }
          });
        }
      });
    });
  },

  // Lấy bản ghi theo ID
  getById: (id, callback) => {
    const sql = `SELECT d.*, c.Name as cncName
                 FROM CncDowntimeNoOrder d
                 LEFT JOIN CncInfor c ON d.cncId = c.Id
                 WHERE d.id = ?`;

    db.get(sql, [id], (err, row) => {
      if (err) {
        callback(err, null);
      } else if (!row) {
        callback(null, null);
      } else {
        const transformedRow = {
          id: row.id,
          cncId: row.cncId,
          downtimeDate: row.downtimeDate,
          downtimeMinutes: row.downtimeMinutes,
          notes: row.notes,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
          createdBy: row.createdBy,
          cncInfo: {
            id: row.cncId,
            Name: row.cncName
          }
        };
        callback(null, transformedRow);
      }
    });
  },

  // Cập nhật bản ghi
  update: (id, data, callback) => {
    const { cncId, downtimeDate, downtimeMinutes, notes, createdBy } = data;
    const sql = `UPDATE CncDowntimeNoOrder
                 SET cncId = ?, downtimeDate = ?, downtimeMinutes = ?, notes = ?, createdBy = ?,
                     updatedAt = datetime('now')
                 WHERE id = ?`;

    db.run(sql, [cncId, downtimeDate, downtimeMinutes, notes, createdBy, id], function (err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Record not found'), null);
      } else {
        // Lấy bản ghi đã cập nhật
        CncDowntimeNoOrder.getById(id, callback);
      }
    });
  },

  // Xóa bản ghi
  delete: (id, callback) => {
    const sql = `DELETE FROM CncDowntimeNoOrder WHERE id = ?`;

    db.run(sql, [id], function (err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Record not found'), null);
      } else {
        callback(null, { success: true, changes: this.changes });
      }
    });
  },

  // Kiểm tra trùng lặp
  checkDuplicate: (cncId, downtimeDate, excludeId = null, callback) => {
    let sql = `SELECT id FROM CncDowntimeNoOrder WHERE cncId = ? AND downtimeDate = ?`;
    let params = [cncId, downtimeDate];

    if (excludeId) {
      sql += ' AND id != ?';
      params.push(excludeId);
    }

    db.get(sql, params, (err, row) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, !!row); // true if duplicate exists
      }
    });
  }
};

module.exports = CncDowntimeNoOrder;
