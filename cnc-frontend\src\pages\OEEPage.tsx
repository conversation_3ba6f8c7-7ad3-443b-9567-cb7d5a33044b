import React, { useEffect, useState, useCallback } from 'react';
import { Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS, ArcElement, Tooltip, Legend, Title
} from 'chart.js';
import { toast } from 'react-toastify';
import { getAllCncInfor, getCncLogs, getStopReasons } from '../services/apiService';
import type { CncLog, StopReason } from '../types';
import { saveToLocalStorage, loadFromLocalStorage } from '../utils/localStorageUtils';
import {
  CHART_COLORS,
    parseMotionTime,
    REASON_CODE_TO_KEY, RUNNING_KEY, // Cần dùng để xác định 'A'
    UNSPECIFIED_STOP_KEY,
} from '../utils/statisticsUtils';
import {
  getMinSelectableDate,
  getMaxSelectableDate,
  getDefaultStartDate,
  getDefaultEndDate,
  validateAndAdjustDate
} from '../utils/dateUtils';
import styles from './OEEPage.module.scss'; // Tạo file SCSS riêng cho trang này
import PasswordModal from '../components/modals/PasswordModal';
import { useAuth } from '../hooks/useAuth';

ChartJS.register(ArcElement, Tooltip, Legend, Title);

// --- Các hằng số và kiểu dữ liệu cho OEE ---
const OEE_STORAGE_KEYS = {
  WORK_START_TIME: 'oee_work_start_time', // e.g., "08:00"
  WORK_END_TIME: 'oee_work_end_time',     // e.g., "17:00"
  BREAK_DURATION_MINUTES: 'oee_break_duration_minutes' // e.g., 60
};

interface WorkingTimeConfig {
  startTime: string; // "HH:MM"
  endTime: string;   // "HH:MM"
  breakMinutes: number;
}

interface OeeMetrics {
  T: number; // Total Production Time (seconds) per day
  A: number; // Planned Downtime (seconds) - Hiện tại lấy từ UNSPECIFIED_STOP
  D: number; // Running Time (seconds) - Từ MotionTime
  C: number; // Total Downtime (seconds) = T - D
  H1: number; // Availability (%)
  H2: number; // Performance (%)
  H3: number; // Quality (%) - ***LƯU Ý: Công thức H3 bạn đưa ra (D/T) giống H1*H2. OEE thường có Quality riêng. Tạm tính theo công thức bạn đưa.***
  // OEE: number; // Overall Equipment Effectiveness (%)
}

interface OeeChartData {
  // oee: ChartJsData | null;
  h1: ChartJsData | null;
  h2: ChartJsData | null;
  h3: ChartJsData | null; // Hoặc tên khác nếu H3 là Quality
}

interface ChartJsData {
  labels: string[];
  datasets: any[];
}

interface ProcessedOeeData {
    cncName: string;
    metrics: OeeMetrics;
    chartData: OeeChartData;
}

// --- Component OEEPage ---

const OEEPage: React.FC = () => {
  const [lastOeeFilters, setLastOeeFilters] = useState({});
  const [cncNameList, setCncNameList] = useState<string[]>([]);
  const [selectedCncName, setSelectedCncName] = useState<string>('Tất cả');
  const [startDate, setStartDate] = useState<string>(getDefaultStartDate());
  const [endDate, setEndDate] = useState<string>(getDefaultEndDate());
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [processedOeeData, setProcessedOeeData] = useState<ProcessedOeeData[]>([]);

  // --- State cho cấu hình thời gian làm việc ---
  const [workConfig, setWorkConfig] = useState<WorkingTimeConfig>(() => ({
    startTime: loadFromLocalStorage(OEE_STORAGE_KEYS.WORK_START_TIME, "08:00"),
    endTime: loadFromLocalStorage(OEE_STORAGE_KEYS.WORK_END_TIME, "17:00"),
    breakMinutes: loadFromLocalStorage(OEE_STORAGE_KEYS.BREAK_DURATION_MINUTES, 60)
  }));
  const [isEditingTime, setIsEditingTime] = useState<boolean>(false);

  // Authentication
  const {
    authState,
    showPasswordModal,
    requestAuth,
    handleAuthSuccess,
    handleAuthCancel,
    modalProps
  } = useAuth();

  // --- useEffects để tải danh sách máy (tương tự Dashboard) ---
   useEffect(() => {
    const fetchCncInfo = async () => {
      try {
        const machines = await getAllCncInfor();
        const names = machines.map(m => m.Name).filter(name => name != null) as string[];
        // Sắp xếp tên máy, đưa "Tất cả" lên đầu
        const sortedNames = [...new Set(names)].sort((a, b) => a.localeCompare(b));
        setCncNameList(['Tất cả', ...sortedNames]);
      } catch (err) {
        console.error('[OEEPage] Failed to fetch CNC names:', err);
        setError('Không thể tải danh sách máy CNC.');
      }
    };
    fetchCncInfo();
  }, []);

  // --- Hàm tính toán T dựa trên cấu hình ---
  const calculateTPerDay = (config: WorkingTimeConfig): number => {
    try {
      const startParts = config.startTime.split(':').map(Number);
      const endParts = config.endTime.split(':').map(Number);

      if (startParts.length !== 2 || endParts.length !== 2 || isNaN(startParts[0]) || isNaN(startParts[1]) || isNaN(endParts[0]) || isNaN(endParts[1])) {
        throw new Error("Invalid time format");
      }

      const startDate = new Date(0, 0, 0, startParts[0], startParts[1], 0);
      let endDate = new Date(0, 0, 0, endParts[0], endParts[1], 0);

      // Handle overnight shifts if necessary (end time is earlier than start time)
      if (endDate < startDate) {
          endDate.setDate(endDate.getDate() + 1); // Add a day
      }

      const totalMilliseconds = endDate.getTime() - startDate.getTime();
      const totalSeconds = totalMilliseconds / 1000;
      const breakSeconds = config.breakMinutes * 60;

      return Math.max(0, totalSeconds - breakSeconds); // Đảm bảo không âm
    } catch (error) {
        console.error("Error calculating T per day:", error);
        // Trả về giá trị mặc định hoặc 0 nếu có lỗi
        // Ví dụ: 8 giờ làm việc trừ 1 giờ nghỉ = 7 giờ = 25200 giây
        return (8 * 3600) - (1 * 3600);
    }
  };


  // --- Hàm xử lý dữ liệu chính để tính OEE ---
  const processOeeData = useCallback((
    allLogs: CncLog[],
    allStopReasons: StopReason[],
    targetCncNameOption: string,
    currentWorkConfig: WorkingTimeConfig
  ): ProcessedOeeData[] => {
    //console.log("[processOeeData] Start processing for OEE - Target:", targetCncNameOption);
    let cncNamesToProcess: string[];
    if (targetCncNameOption === 'Tất cả') {
      cncNamesToProcess = Array.from(new Set(allLogs.map(log => log.CncName).filter(name => name != null))) as string[];
    } else {
      cncNamesToProcess = [targetCncNameOption];
    }
    //console.log("[processOeeData] CNC names to process:", cncNamesToProcess);

    const T_per_day = calculateTPerDay(currentWorkConfig); // Tính T cho 1 ngày
    //console.log(`[processOeeData] Calculated T per day: ${T_per_day} seconds`);
    const results: ProcessedOeeData[] = [];

    for (const currentCncName of cncNamesToProcess) {
      //console.log(`[processOeeData] Processing CNC: ${currentCncName}`);
      const logsForCnc = allLogs
        .filter(log => log.CncName === currentCncName && log.MotionTime != null && log.MotionTime.trim() !== '')
        .sort((a, b) => new Date(a.UpdateAt).getTime() - new Date(b.UpdateAt).getTime());

      const reasonsForCnc = allStopReasons.filter(sr => sr.CncName === currentCncName && sr.StopTime && sr.ConfirmTime && sr.ReasonCode !== null);

      if (logsForCnc.length < 2) { // Cần ít nhất 2 log để tính D
        //console.log(`[processOeeData] Not enough logs with MotionTime for ${currentCncName}`);
        continue;
      }

      // Xác định số ngày hoạt động thực tế trong khoảng thời gian chọn
      const logDates = new Set(logsForCnc.map(log => new Date(log.UpdateAt).toISOString().split('T')[0]));
      const actualLogDays = logDates.size;
      if (actualLogDays === 0) {
        //console.log(`[processOeeData] No active log days for ${currentCncName}`);
        continue;
      }

      const total_T = actualLogDays * T_per_day; // Tổng T cho toàn bộ khoảng thời gian

      // Tính tổng thời gian chạy (D)
      const firstLog = logsForCnc[0];
      const lastLog = logsForCnc[logsForCnc.length - 1];
      const firstMotion = parseMotionTime(firstLog.MotionTime);
      const lastMotion = parseMotionTime(lastLog.MotionTime);
      let total_D = Math.max(0, lastMotion - firstMotion); // Tổng thời gian chạy (D)

      // Tính tổng thời gian dừng không xác định (A) - Dựa trên StopReasons
      let total_A_unspecified = 0;
      reasonsForCnc.forEach(sr => {
          const standardKey = REASON_CODE_TO_KEY[sr.ReasonCode!] || UNSPECIFIED_STOP_KEY;
          // *** QUAN TRỌNG: Logic xác định 'A' cần xem xét lại.
          // Hiện tại, đang lấy tất cả thời gian dừng không rõ lý do (UNSPECIFIED_STOP_KEY) làm A.
          // Theo định nghĩa OEE, A thường là 'Planned Downtime' (dừng có kế hoạch).
          // Nếu UNSPECIFIED_STOP_KEY bao gồm cả dừng không kế hoạch thì cách tính này chưa chuẩn OEE.
          // Tạm thời giữ nguyên logic này theo yêu cầu "mặc định là thời gian dừng không rõ lý do".
          if (standardKey === UNSPECIFIED_STOP_KEY) {
              const stopTime = new Date(sr.StopTime!).getTime();
              const confirmTime = new Date(sr.ConfirmTime!).getTime();
              const durationSeconds = Math.max(0, (confirmTime - stopTime) / 1000);
              total_A_unspecified += durationSeconds;
          }
      });

      const total_A = total_A_unspecified; // Gán A

      // Tính toán các chỉ số OEE
      const total_C = Math.max(0, total_T - total_D); // Tổng thời gian dừng = T - D

      // H1: Availability = (Thời gian hoạt động thực tế) / (Tổng thời gian sản xuất kế hoạch)
      // Thời gian hoạt động thực tế = T - A (Thời gian dừng có kế hoạch)
      const H1 = total_T > 0 ? Math.max(0, Math.min(100, ((total_T - total_A) / total_T) * 100)) : 0;

      // H2: Performance = (Thời gian chạy máy thực tế) / (Thời gian máy nên chạy)
      // Thời gian máy nên chạy = T - A (Vì máy chỉ nên chạy khi không có kế hoạch dừng)
      const plannedOperatingTime = total_T - total_A;
      const H2 = plannedOperatingTime > 0 ? Math.max(0, Math.min(100, (total_D / plannedOperatingTime) * 100)) : 0;

      // H3: Quality (Theo công thức bạn cung cấp D/T) - **Cảnh báo: Đây thường không phải là Quality trong OEE chuẩn**
      // Quality chuẩn = (Sản phẩm tốt) / (Tổng sản phẩm). Không có dữ liệu này.
      // Tính theo công thức D/T
      const H3_from_formula = total_T > 0 ? Math.max(0, Math.min(100, (total_D / total_T) * 100)) : 0;

      // OEE = H1 * H2 * H3 (Các H tính theo %)
      // const OEE = (H1 / 100) * (H2 / 100) * (H3_from_formula / 100) * 100;

      const metrics: OeeMetrics = {
          T: total_T,
          A: total_A,
          D: total_D,
          C: total_C,
          H1: parseFloat(H1.toFixed(2)),
          H2: parseFloat(H2.toFixed(2)),
          H3: parseFloat(H3_from_formula.toFixed(2)),
          // OEE: parseFloat(OEE.toFixed(2)),
      };

      // Tạo dữ liệu cho biểu đồ Pie
       const createPieData = (value: number, label: string): ChartJsData | null => {
            if (value < 0 || value > 100) return null;
            return {
                labels: [label, 'Còn lại'],
                datasets: [{
                    data: [value, 100 - value],
                    backgroundColor: ['rgba(0, 102, 255, 0.9)', 'rgba(220, 0, 0, 0.9)'], // Màu cho giá trị và phần còn lại
                    borderColor: ['rgba(75, 192, 192, 1)', 'rgba(211, 211, 211, 1)'],
                    borderWidth: 1,
                }]
            };
        };

      const chartData: OeeChartData = {
        // oee: createPieData(metrics.OEE, `OEE: ${metrics.OEE}%`),
        h1: createPieData(metrics.H1, `H1 (Sẵn sàng): ${metrics.H1}%`),
        h2: createPieData(metrics.H2, `H2 (Hiệu suất): ${metrics.H2}%`),
        h3: createPieData(metrics.H3, `H3 (Chất lượng - D/T): ${metrics.H3}%`),
      };

      results.push({ cncName: currentCncName, metrics, chartData });
    }
    //console.log("[processOeeData] Processing finished. Results:", results);
    return results;

  }, []); // Thêm dependency nếu cần

 // --- Hàm tải dữ liệu (tương tự Dashboard, gọi processOeeData) ---
  const handleLoadData = useCallback(async (triggeredByButton = false) => {
    //console.log("[handleLoadData OEE] Attempting load:", { startDate, endDate, selectedCncName });
    setIsLoading(true);
    setError(null);
    setProcessedOeeData([]); // Clear old data

    const currentFilters = { startDate, endDate, selectedCncName, workConfig: JSON.stringify(workConfig) }; // Include workConfig change
    const filtersChanged = JSON.stringify(currentFilters) !== JSON.stringify(lastOeeFilters);

    try {
        const cncNameToFetch = selectedCncName === 'Tất cả' ? undefined : selectedCncName;
        const apiParams = {
          cncName: cncNameToFetch,
          startDate: startDate,
          endDate: endDate,
          isAll: 'true' // Lấy tất cả log/reason trong khoảng thời gian
        };

        //console.log("[handleLoadData OEE] Fetching logs with params:", apiParams);
        const logsResponse = await getCncLogs(apiParams);
        const allLogsForProcessing = logsResponse.logs;
        //console.log("[handleLoadData OEE] Fetched logs:", allLogsForProcessing.length);

        //console.log("[handleLoadData OEE] Fetching reasons with params:", apiParams);
        const reasonsResponse = await getStopReasons(apiParams);
        const allReasonsForProcessing = reasonsResponse.reasons.filter(r => r.ConfirmTime); // Chỉ lấy reason đã confirm để tính A (cần xem lại logic A)
        //console.log("[handleLoadData OEE] Fetched confirmed reasons:", allReasonsForProcessing.length);

        if (allLogsForProcessing.length === 0) {
            setError("Không có dữ liệu log để tính toán OEE trong khoảng thời gian đã chọn.");
            if (triggeredByButton || filtersChanged) {
                toast.info("Không tìm thấy dữ liệu OEE phù hợp.");
            }
            setIsLoading(false);
            return;
        }

        const processedData = processOeeData(allLogsForProcessing, allReasonsForProcessing, selectedCncName, workConfig);

        if (processedData.length === 0) {
            setError("Không thể xử lý dữ liệu OEE cho các lựa chọn hiện tại (có thể do thiếu log hoặc cấu hình).");
            if (triggeredByButton || filtersChanged) {
                toast.warning("Không thể tính toán OEE cho lựa chọn này.");
            }
        }

        setProcessedOeeData(processedData);
        //console.log("[handleLoadData OEE] Setting processed OEE data:", processedData);
        if (triggeredByButton && filtersChanged) {
            toast.success("Cập nhật dữ liệu OEE thành công!");
        }

    } catch (err) {
        console.error('[OEEPage] Error loading OEE data:', err);
        const errorMsg = err instanceof Error ? err.message : 'Lỗi không xác định khi tải dữ liệu OEE.';
        setError(errorMsg);
        setProcessedOeeData([]); // Clear data on error
        if (triggeredByButton || filtersChanged) {
            toast.error(`Lỗi tải dữ liệu OEE: ${errorMsg}`);
        }
    } finally {
        setIsLoading(false);
    }
  }, [startDate, endDate, selectedCncName, workConfig, processOeeData, lastOeeFilters, setLastOeeFilters]); // Include dependencies

  // --- useEffect để tải dữ liệu khi filter thay đổi (tương tự Dashboard) ---
  useEffect(() => {
    if (cncNameList.length > 0) { // Chỉ tải khi đã có danh sách máy
      handleLoadData(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cncNameList, startDate, endDate, selectedCncName, workConfig]); // Thêm workConfig làm dependency

  // --- Hàm xử lý thay đổi cấu hình thời gian ---
  const handleTimeConfigChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setWorkConfig(prev => ({
        ...prev,
        [name]: name === 'breakMinutes' ? parseInt(value, 10) : value
    }));
  };

  const handleSaveTimeConfig = () => {
    try {
        saveToLocalStorage(OEE_STORAGE_KEYS.WORK_START_TIME, workConfig.startTime);
        saveToLocalStorage(OEE_STORAGE_KEYS.WORK_END_TIME, workConfig.endTime);
        saveToLocalStorage(OEE_STORAGE_KEYS.BREAK_DURATION_MINUTES, workConfig.breakMinutes);
        setIsEditingTime(false);
        handleLoadData(); // Tải lại dữ liệu với cấu hình mới
        toast.success(`Lưu cấu hình thời gian thành công! (${authState.userRole})`);
        //console.log("Saved work config:", workConfig);
    } catch (error) {
        console.error("Failed to save time config:", error);
        toast.error("Lưu cấu hình thời gian thất bại!");
    }
  };

  const handleEditTimeConfig = () => {
    requestAuth(() => {
      setIsEditingTime(true);
    }, 'Xác thực để chỉnh sửa cấu hình', 'Vui lòng nhập mật khẩu để chỉnh sửa cấu hình thời gian làm việc:');
  };

  // --- Options cho biểu đồ Pie ---
   const pieChartOptions = (titleText: string) => ({
        responsive: true,
        maintainAspectRatio: false, // Quan trọng để kiểm soát kích thước
        plugins: {
            datalabels: {
              display: false // Tắt hiển thị trên biểu đồ
            },
            legend: {
                display: false, // Tắt chú giải mặc định để tiết kiệm không gian
                // position: 'top' as const,
            },
            backgroundColor: CHART_COLORS[RUNNING_KEY],
            title: {
                display: true,
                text: titleText,
                font: {
                    size: 10 // Giảm kích thước font tiêu đề
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context: any) {
                        let label = context.label || '';
                        if (label) {
                            label += ': ';
                        }
                        if (context.parsed !== null) {
                            // Lấy giá trị % từ data, không phải label
                            label += context.dataset.data[context.dataIndex].toFixed(1) + '%';
                        }
                        return label;
                    }
                }
            }
        },
    });

  // --- JSX để render ---
  return (
    <div className={styles.oeePageContainer}>
      {/* Phần cấu hình thời gian làm việc */}
      <div className={styles.timeConfigContainer}>
          <div className={styles.timeConfigInputs}>
            <h3>Cấu hình thời gian làm việc (tính T)</h3>
              <div>
                  <label htmlFor="startTime">Bắt đầu:</label>
                  <input
                      type="time"
                      id="startTime"
                      name="startTime"
                      value={workConfig.startTime}
                      onChange={handleTimeConfigChange}
                      disabled={!isEditingTime}
                  />
              </div>
              <div>
                  <label htmlFor="endTime">Kết thúc:</label>
                  <input
                      type="time"
                      id="endTime"
                      name="endTime"
                      value={workConfig.endTime}
                      onChange={handleTimeConfigChange}
                      disabled={!isEditingTime}
                  />
              </div>
              <div>
                  <label htmlFor="breakMinutes">Nghỉ (phút):</label>
                  <input
                      type="number"
                      id="breakMinutes"
                      name="breakMinutes"
                      value={workConfig.breakMinutes}
                      onChange={handleTimeConfigChange}
                      min="0"
                      step="1"
                      disabled={!isEditingTime}
                  />
              </div>
              <div>
                  {isEditingTime ? (
                      <button onClick={handleSaveTimeConfig}>Lưu</button>
                  ) : (
                      <button onClick={handleEditTimeConfig}>Chỉnh sửa</button>
                  )}
              </div>
          </div>
          <div className={styles.calculatedT}>
            <h3>Chú thích</h3>
            <p>T : Tổng thời gian sản xuất mỗi ngày : {(calculateTPerDay(workConfig) / 3600).toFixed(2)} giờ</p>
            <p>A : Dừng không có đơn hàng : (người dùng nhập),tạm thời quy thành dừng không rõ lý do</p>
            <p>C : Tổng thời gian dừng máy ≠ Status Busy : T-D (Máy chạy &gt; Status Busy &gt; MotionTime tăng)</p>
            <p>D : Tổng thời gian máy làm việc</p>
            <p>H1: Hiệu suất kinh doanh (1-A/T)*100</p>
            <p>H2: Hiệu suất sản xuất (D )/(T-A)*100</p>
            <p>H3: Hiệu suất tổng thể D/T*100 </p>
          </div>
        </div>

      {/* Bộ lọc ngày và chọn máy (tương tự Dashboard) */}
      <div className={styles.filtersContainer}>
        {/* Start Date */}
        <div>
          <label htmlFor="oeeStartDate">Từ ngày:</label>
          <input
            type="date"
            id="oeeStartDate"
            value={startDate}
            onChange={(e) => setStartDate(validateAndAdjustDate(e.target.value))}
            min={getMinSelectableDate()}
            max={endDate < getMaxSelectableDate() ? endDate : getMaxSelectableDate()} // Ngăn chọn ngày bắt đầu sau ngày kết thúc hoặc tương lai
          />
        </div>
        {/* End Date */}
        <div>
          <label htmlFor="oeeEndDate">Đến ngày:</label>
          <input
            type="date"
            id="oeeEndDate"
            value={endDate}
            onChange={(e) => setEndDate(validateAndAdjustDate(e.target.value))}
            min={startDate > getMinSelectableDate() ? startDate : getMinSelectableDate()} // Ngăn chọn ngày kết thúc trước ngày bắt đầu hoặc trước ngày tối thiểu
            max={getMaxSelectableDate()} // Ngăn chọn ngày tương lai
          />
        </div>
        {/* CNC Selector */}
        <div>
          <label htmlFor="oeeCncName">Chọn máy CNC:</label>
          <select
            id="oeeCncName"
            value={selectedCncName}
            onChange={(e) => setSelectedCncName(e.target.value)}
            disabled={cncNameList.length <= 1} // Disable nếu chỉ có "Tất cả"
          >
            {cncNameList.map(name => <option key={name} value={name}>{name}</option>)}
          </select>
        </div>
         {/* Nút Load Data */}
         <button onClick={() => handleLoadData(true)} disabled={isLoading}>
           {isLoading ? 'Đang tải...' : 'Xem OEE'}
         </button>
      </div>

      {/* Hiển thị lỗi hoặc loading */}
      {isLoading && <div className={styles.loadingMessage}>Đang tải và tính toán OEE...</div>}
      {error && <div className={styles.errorMessage}>{error}</div>}

      {/* Hiển thị kết quả OEE */}
      {!isLoading && !error && processedOeeData.length > 0 && (
        <div className={styles.resultsContainer}>
          {processedOeeData.map((data) => (
            <div key={data.cncName} className={styles.oeeCard}>
              <h2>{data.cncName}</h2>
              <div className={styles.chartGrid}>
                {/* OEE Chart (Lớn hơn) */}
                {/* <div className={`${styles.chartItem} ${styles.oeeMainChart}`}>
                  {data.chartData.oee ? (
                     <Pie options={pieChartOptions(`OEE: ${data.metrics.OEE}%`)} data={data.chartData.oee} />
                  ) : <p>N/A</p>}
                </div> */}
                {/* H1 Chart */}
                <div className={styles.chartItem}>
                  {data.chartData.h1 ? (
                    <Pie options={pieChartOptions(`H1: ${data.metrics.H1}%`)} data={data.chartData.h1} />
                  ) : <p>N/A</p>}
                </div>
                {/* H2 Chart */}
                <div className={styles.chartItem}>
                  {data.chartData.h2 ? (
                     <Pie options={pieChartOptions(`H2: ${data.metrics.H2}%`)} data={data.chartData.h2} />
                  ) : <p>N/A</p>}
                </div>
                {/* H3 Chart */}
                <div className={styles.chartItem}>
                  {data.chartData.h3 ? (
                    <Pie options={pieChartOptions(`H3: ${data.metrics.H3}%`)} data={data.chartData.h3} />
                  ) : <p>N/A</p>}
                </div>
              </div>
               {/* Optional: Display raw numbers */}
               <div className={styles.metricsDetails}>
                   <p>T: {(data.metrics.T / 3600).toFixed(2)}h | A: {(data.metrics.A / 3600).toFixed(2)}h | D: {(data.metrics.D / 3600).toFixed(2)}h | C: {(data.metrics.C / 3600).toFixed(2)}h</p>
               </div>
            </div>
          ))}
        </div>
      )}
      {/* Thông báo không có dữ liệu */}
       {!isLoading && !error && processedOeeData.length === 0 && (
            <div className={styles.noDataMessage}>
                Không có dữ liệu OEE để hiển thị cho lựa chọn hiện tại. Vui lòng kiểm tra bộ lọc hoặc cấu hình thời gian.
            </div>
        )}

      {/* Password Modal */}
      <PasswordModal
        isOpen={showPasswordModal}
        onClose={handleAuthCancel}
        onSuccess={handleAuthSuccess}
        title={modalProps.title}
        message={modalProps.message}
      />
    </div>
  );
};

export default OEEPage;