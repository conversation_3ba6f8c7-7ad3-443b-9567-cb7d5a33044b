import React from 'react';
import { Link } from 'react-router-dom';
import styles from './Layout.module.scss';
import { FaTachometerAlt, FaCogs, FaClipboardList, FaChartPie, FaClock } from 'react-icons/fa';

interface SidebarProps {
  setIsHovered: (isHovered: boolean) => void;
  expandedClass: string; // To be set by App.tsx (styles.sidebarExpanded or styles.sidebarCollapsed)
}

const Sidebar: React.FC<SidebarProps> = ({ setIsHovered, expandedClass }) => {
  const isExpanded = expandedClass === styles.sidebarExpanded;

  return (
    <aside 
      className={`${styles.sidebar} ${expandedClass}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className={styles.logoContainer}>
        <img 
          src={isExpanded ? "/intech.png" : "/intechicon.ico"} 
          alt="Logo" 
          className={isExpanded ? styles.logoFull : styles.logoIcon}
        />
      </div>
      <nav>
        <ul>
          <li>
            <Link to="/">
              <FaTachometerAlt className={styles.sidebarIcon} /> 
              <span className={styles.navText}>Dashboard</span>
            </Link>
          </li>
          <li> {/* OEE Page */}
            <Link to="/oee">
              <FaChartPie className={styles.sidebarIcon} />
              <span className={styles.navText}>Phân tích OEE</span>
            </Link>
          </li>
          <li>{/* Quản lý máy */}
            <Link to="/management">
              <FaCogs className={styles.sidebarIcon} />
              <span className={styles.navText}>Quản lý máy</span>
            </Link>
          </li>
          <li> {/* Quản lý Logs */}
            <Link to="/logs">
              <FaClipboardList className={styles.sidebarIcon} />
              <span className={styles.navText}>Quản lý Logs</span>
            </Link>
          </li>
          <li> {/* Quản lý Stop ReasonReason */}
            <Link to="/stopreason">
              <FaClipboardList className={styles.sidebarIcon} />
              <span className={styles.navText}>Quản lý dừng máy </span>
            </Link>
          </li>
          <li> {/* Quản lý thời gian dừng không có đơn hàng */}
            <Link to="/downtime-no-order">
              <FaClock className={styles.sidebarIcon} />
              <span className={styles.navText}>Thời gian dừng</span>
            </Link>
          </li>
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar; 