import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { getUserActionLogs, getUserStats, getTableStats } from '../services/apiService';
import type { UserActionLog, UserStats, TableStats } from '../types';
import {
  getMinSelectableDate,
  getMaxSelectableDate,
  getDefaultStartDate,
  getDefaultEndDate,
  validateAndAdjustDate
} from '../utils/dateUtils';
import styles from './UserActionLogPage.module.scss';

const UserActionLogPage: React.FC = () => {
  const [actionLogs, setActionLogs] = useState<UserActionLog[]>([]);
  const [userStats, setUserStats] = useState<UserStats[]>([]);
  const [tableStats, setTableStats] = useState<TableStats[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const pageSize = 20;

  // Filters
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [selectedActionType, setSelectedActionType] = useState<string>('');
  const [selectedTableName, setSelectedTableName] = useState<string>('');
  const [startDate, setStartDate] = useState<string>(getDefaultStartDate());
  const [endDate, setEndDate] = useState<string>(getDefaultEndDate());

  // View mode
  const [viewMode, setViewMode] = useState<'logs' | 'userStats' | 'tableStats'>('logs');

  // Load action logs
  const loadActionLogs = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const params = {
        page: currentPage,
        limit: pageSize,
        startDate,
        endDate,
        ...(selectedUserId && { userId: selectedUserId }),
        ...(selectedActionType && { actionType: selectedActionType }),
        ...(selectedTableName && { tableName: selectedTableName })
      };

      const response = await getUserActionLogs(params);
      setActionLogs(response.data);
      setTotalPages(response.pagination.totalPages);
      setTotalRecords(response.pagination.total);
    } catch (err) {
      console.error('Error loading action logs:', err);
      setError('Không thể tải dữ liệu lịch sử thao tác');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, startDate, endDate, selectedUserId, selectedActionType, selectedTableName]);

  // Load stats
  const loadStats = useCallback(async () => {
    try {
      const [userStatsResponse, tableStatsResponse] = await Promise.all([
        getUserStats(),
        getTableStats()
      ]);
      setUserStats(userStatsResponse.data);
      setTableStats(tableStatsResponse.data);
    } catch (err) {
      console.error('Error loading stats:', err);
      toast.error('Không thể tải thống kê');
    }
  }, []);

  useEffect(() => {
    if (viewMode === 'logs') {
      loadActionLogs();
    } else {
      loadStats();
    }
  }, [viewMode, loadActionLogs, loadStats]);

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const getActionTypeColor = (actionType: string): string => {
    switch (actionType) {
      case 'CREATE': return '#28a745';
      case 'UPDATE': return '#ffc107';
      case 'DELETE': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getActionTypeText = (actionType: string): string => {
    switch (actionType) {
      case 'CREATE': return 'Tạo mới';
      case 'UPDATE': return 'Cập nhật';
      case 'DELETE': return 'Xóa';
      default: return actionType;
    }
  };

  const getTableDisplayName = (tableName: string): string => {
    switch (tableName) {
      case 'CncInfor': return 'Máy CNC';
      case 'CncDowntimeNoOrder': return 'Thời gian dừng';
      case 'StopReasons': return 'Lý do dừng máy';
      default: return tableName;
    }
  };

  return (
    <div className={styles.pageContainer}>
      <div className={styles.header}>
        <h1>Lịch sử thao tác người dùng</h1>
        <div className={styles.viewModeButtons}>
          <button 
            className={viewMode === 'logs' ? styles.active : ''}
            onClick={() => setViewMode('logs')}
          >
            Lịch sử
          </button>
          <button 
            className={viewMode === 'userStats' ? styles.active : ''}
            onClick={() => setViewMode('userStats')}
          >
            Thống kê User
          </button>
          <button 
            className={viewMode === 'tableStats' ? styles.active : ''}
            onClick={() => setViewMode('tableStats')}
          >
            Thống kê Bảng
          </button>
        </div>
      </div>

      {viewMode === 'logs' && (
        <>
          {/* Filters */}
          <div className={styles.filtersContainer}>
            <div>
              <label htmlFor="userIdFilter">User:</label>
              <input
                type="text"
                id="userIdFilter"
                value={selectedUserId}
                onChange={(e) => setSelectedUserId(e.target.value)}
                placeholder="Nhập tên user..."
              />
            </div>
            <div>
              <label htmlFor="actionTypeFilter">Loại thao tác:</label>
              <select
                id="actionTypeFilter"
                value={selectedActionType}
                onChange={(e) => setSelectedActionType(e.target.value)}
              >
                <option value="">Tất cả</option>
                <option value="CREATE">Tạo mới</option>
                <option value="UPDATE">Cập nhật</option>
                <option value="DELETE">Xóa</option>
              </select>
            </div>
            <div>
              <label htmlFor="tableNameFilter">Bảng:</label>
              <select
                id="tableNameFilter"
                value={selectedTableName}
                onChange={(e) => setSelectedTableName(e.target.value)}
              >
                <option value="">Tất cả</option>
                <option value="CncInfor">Máy CNC</option>
                <option value="CncDowntimeNoOrder">Thời gian dừng</option>
                <option value="StopReasons">Lý do dừng máy</option>
              </select>
            </div>
            <div>
              <label htmlFor="startDate">Từ ngày:</label>
              <input
                type="date"
                id="startDate"
                value={startDate}
                onChange={(e) => setStartDate(validateAndAdjustDate(e.target.value))}
                min={getMinSelectableDate()}
                max={endDate < getMaxSelectableDate() ? endDate : getMaxSelectableDate()}
              />
            </div>
            <div>
              <label htmlFor="endDate">Đến ngày:</label>
              <input
                type="date"
                id="endDate"
                value={endDate}
                onChange={(e) => setEndDate(validateAndAdjustDate(e.target.value))}
                min={startDate > getMinSelectableDate() ? startDate : getMinSelectableDate()}
                max={getMaxSelectableDate()}
              />
            </div>
            <button onClick={() => setCurrentPage(1)}>Lọc</button>
          </div>

          {error && <div className={styles.error}>{error}</div>}

          {/* Data Table */}
          <div className={styles.tableContainer}>
            {isLoading ? (
              <div className={styles.loading}>Đang tải dữ liệu...</div>
            ) : (
              <>
                <table className={styles.table}>
                  <thead>
                    <tr>
                      <th>Thời gian</th>
                      <th>User</th>
                      <th>Thao tác</th>
                      <th>Bảng</th>
                      <th>Mô tả</th>
                      <th>Chi tiết</th>
                    </tr>
                  </thead>
                  <tbody>
                    {actionLogs.map(log => (
                      <tr key={log.id}>
                        <td>{formatDate(log.actionDate)}</td>
                        <td>
                          <span className={styles.userBadge}>{log.userId}</span>
                        </td>
                        <td>
                          <span 
                            className={styles.actionBadge}
                            style={{ backgroundColor: getActionTypeColor(log.actionType) }}
                          >
                            {getActionTypeText(log.actionType)}
                          </span>
                        </td>
                        <td>{getTableDisplayName(log.tableName)}</td>
                        <td>{log.recordDescription}</td>
                        <td>
                          {log.actionDetails ? (
                            <details className={styles.details}>
                              <summary>Xem chi tiết</summary>
                              <pre>{log.actionDetails}</pre>
                            </details>
                          ) : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {/* Pagination */}
                <div className={styles.pagination}>
                  <span>Trang {currentPage} / {totalPages} (Tổng: {totalRecords} bản ghi)</span>
                  <div>
                    <button 
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Trước
                    </button>
                    <button 
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Sau
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </>
      )}

      {viewMode === 'userStats' && (
        <div className={styles.statsContainer}>
          <h2>Thống kê theo User</h2>
          <div className={styles.statsGrid}>
            {userStats.map(stat => (
              <div key={stat.userId} className={styles.statCard}>
                <h3>{stat.userId}</h3>
                <div className={styles.statNumbers}>
                  <div>Tổng: <strong>{stat.totalActions}</strong></div>
                  <div>Tạo: <span style={{color: '#28a745'}}>{stat.createCount}</span></div>
                  <div>Sửa: <span style={{color: '#ffc107'}}>{stat.updateCount}</span></div>
                  <div>Xóa: <span style={{color: '#dc3545'}}>{stat.deleteCount}</span></div>
                </div>
                <div className={styles.lastAction}>
                  Lần cuối: {formatDate(stat.lastAction)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {viewMode === 'tableStats' && (
        <div className={styles.statsContainer}>
          <h2>Thống kê theo Bảng</h2>
          <div className={styles.statsGrid}>
            {tableStats.map(stat => (
              <div key={stat.tableName} className={styles.statCard}>
                <h3>{getTableDisplayName(stat.tableName)}</h3>
                <div className={styles.statNumbers}>
                  <div>Tổng: <strong>{stat.totalActions}</strong></div>
                  <div>Tạo: <span style={{color: '#28a745'}}>{stat.createCount}</span></div>
                  <div>Sửa: <span style={{color: '#ffc107'}}>{stat.updateCount}</span></div>
                  <div>Xóa: <span style={{color: '#dc3545'}}>{stat.deleteCount}</span></div>
                </div>
                <div className={styles.lastAction}>
                  Lần cuối: {formatDate(stat.lastAction)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default UserActionLogPage;
