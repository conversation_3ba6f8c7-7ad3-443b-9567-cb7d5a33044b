import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { getAllCncInfor, getDowntimeNoOrders, createDowntimeNoOrder, updateDowntimeNoOrder, deleteDowntimeNoOrder } from '../services/apiService';
import type { CncInfor, CncDowntimeNoOrder, CncDowntimeNoOrderFormData } from '../types';
import {
  getMinSelectableDate,
  getMaxSelectableDate,
  getDefaultStartDate,
  getDefaultEndDate,
  validateAndAdjustDate
} from '../utils/dateUtils';
import styles from './DowntimeNoOrderPage.module.scss';
import DowntimeFormModal from '../components/modals/DowntimeFormModal';
import PasswordModal from '../components/modals/PasswordModal';
import { useAuth } from '../hooks/useAuth';

const DowntimeNoOrderPage: React.FC = () => {
  const [cncList, setCncList] = useState<CncInfor[]>([]);
  const [downtimeRecords, setDowntimeRecords] = useState<CncDowntimeNoOrder[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Authentication
  const {
    authState,
    showPasswordModal,
    requestAuth,
    handleAuthSuccess,
    handleAuthCancel,
    modalProps
  } = useAuth();
  
  // Pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const pageSize = 20;

  // Filters
  const [selectedCncId, setSelectedCncId] = useState<number | ''>('');
  const [startDate, setStartDate] = useState<string>(getDefaultStartDate());
  const [endDate, setEndDate] = useState<string>(getDefaultEndDate());

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [editingRecord, setEditingRecord] = useState<CncDowntimeNoOrder | null>(null);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [isSubmittingForm, setIsSubmittingForm] = useState<boolean>(false);

  // Load CNC list
  useEffect(() => {
    const fetchCncList = async () => {
      try {
        const cncs = await getAllCncInfor();
        setCncList(cncs);
      } catch (err) {
        console.error('Error fetching CNC list:', err);
        setError('Không thể tải danh sách máy CNC');
      }
    };
    fetchCncList();
  }, []);

  // Load downtime records
  const loadDowntimeRecords = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const params = {
        page: currentPage,
        limit: pageSize,
        startDate,
        endDate,
        ...(selectedCncId && { cncId: Number(selectedCncId) })
      };

      const response = await getDowntimeNoOrders(params);
      setDowntimeRecords(response.data);
      setTotalPages(response.pagination.totalPages);
      setTotalRecords(response.pagination.total);
    } catch (err) {
      console.error('Error loading downtime records:', err);
      setError('Không thể tải dữ liệu thời gian dừng');
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, startDate, endDate, selectedCncId]);

  useEffect(() => {
    loadDowntimeRecords();
  }, [loadDowntimeRecords]);

  // Modal handlers
  const handleOpenModalForCreate = () => {
    requestAuth(() => {
      setIsEditMode(false);
      setEditingRecord(null);
      setIsModalOpen(true);
    }, 'Xác thực để thêm mới', 'Vui lòng nhập mật khẩu để thêm bản ghi thời gian dừng mới:');
  };

  const handleOpenModalForEdit = (record: CncDowntimeNoOrder) => {
    requestAuth(() => {
      setIsEditMode(true);
      setEditingRecord(record);
      setIsModalOpen(true);
    }, 'Xác thực để chỉnh sửa', 'Vui lòng nhập mật khẩu để chỉnh sửa bản ghi thời gian dừng:');
  };

  const handleCloseModal = () => {
    setTimeout(() => {
      setIsModalOpen(false);
      setEditingRecord(null);
    }, 300);
  };

  const handleFormSubmit = async (formData: CncDowntimeNoOrderFormData, id?: number) => {
    setIsSubmittingForm(true);

    // Add user role to form data
    const formDataWithUser = {
      ...formData,
      createdBy: authState.userRole || 'Unknown'
    };

    try {
      if (isEditMode && id) {
        await updateDowntimeNoOrder(id, formDataWithUser);
        toast.success(`Cập nhật bản ghi thành công (${authState.userRole})`);
      } else {
        await createDowntimeNoOrder(formDataWithUser);
        toast.success(`Tạo bản ghi thành công (${authState.userRole})`);
      }

      handleCloseModal();
      loadDowntimeRecords();
    } catch (err: any) {
      console.error('Error saving record:', err);
      toast.error(err.response?.data?.message || 'Lỗi khi lưu bản ghi');
    } finally {
      setIsSubmittingForm(false);
    }
  };

  const handleDelete = (id: number) => {
    requestAuth(async () => {
      if (!window.confirm('Bạn có chắc chắn muốn xóa bản ghi này?')) {
        return;
      }

      try {
        await deleteDowntimeNoOrder(id);
        toast.success(`Xóa bản ghi thành công (${authState.userRole})`);
        loadDowntimeRecords();
      } catch (err: any) {
        console.error('Error deleting record:', err);
        toast.error(err.response?.data?.message || 'Lỗi khi xóa bản ghi');
      }
    }, 'Xác thực để xóa', 'Vui lòng nhập mật khẩu để xóa bản ghi thời gian dừng:');
  };

  const formatMinutesToHours = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h${mins.toString().padStart(2, '0')}m`;
  };

  return (
    <div className={styles.pageContainer}>
      <div className={styles.header}>
        <h1>Quản lý thời gian dừng</h1>
        <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
          {authState.isAuthenticated && (
            <span style={{
              fontSize: '0.9rem',
              color: 'var(--text-secondary-color, #555)',
              padding: '5px 10px',
              backgroundColor: 'rgba(0, 123, 255, 0.1)',
              borderRadius: '4px'
            }}>
              Đăng nhập: {authState.userRole}
            </span>
          )}
          <button
            className={styles.addButton}
            onClick={handleOpenModalForCreate}
          >
            Thêm mới
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className={styles.filtersContainer}>
        <div>
          <label htmlFor="cncSelect">Máy CNC:</label>
          <select
            id="cncSelect"
            value={selectedCncId}
            onChange={(e) => setSelectedCncId(e.target.value === '' ? '' : Number(e.target.value))}
          >
            <option value="">Tất cả máy</option>
            {cncList.map(cnc => (
              <option key={cnc.Id} value={cnc.Id}>{cnc.Name}</option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="startDate">Từ ngày:</label>
          <input
            type="date"
            id="startDate"
            value={startDate}
            onChange={(e) => setStartDate(validateAndAdjustDate(e.target.value))}
            min={getMinSelectableDate()}
            max={endDate < getMaxSelectableDate() ? endDate : getMaxSelectableDate()}
          />
        </div>
        <div>
          <label htmlFor="endDate">Đến ngày:</label>
          <input
            type="date"
            id="endDate"
            value={endDate}
            onChange={(e) => setEndDate(validateAndAdjustDate(e.target.value))}
            min={startDate > getMinSelectableDate() ? startDate : getMinSelectableDate()}
            max={getMaxSelectableDate()}
          />
        </div>
        <button onClick={() => setCurrentPage(1)}>Lọc</button>
      </div>

      {error && <div className={styles.error}>{error}</div>}



      {/* Data Table */}
      <div className={styles.tableContainer}>
        {isLoading ? (
          <div className={styles.loading}>Đang tải dữ liệu...</div>
        ) : (
          <>
            <table className={styles.table}>
              <thead>
                <tr>
                  <th>Máy CNC</th>
                  <th>Ngày</th>
                  <th>Thời gian dừng</th>
                  <th>Ghi chú</th>
                  <th>Người tạo</th>
                  <th>Ngày tạo</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
                {downtimeRecords.map(record => (
                  <tr key={record.id}>
                    <td>{record.cncInfo?.Name || 'N/A'}</td>
                    <td>{record.downtimeDate}</td>
                    <td>{formatMinutesToHours(record.downtimeMinutes)}</td>
                    <td>{record.notes || '-'}</td>
                    <td>{record.createdBy || '-'}</td>
                    <td>{new Date(record.createdAt).toLocaleDateString('vi-VN')}</td>
                    <td>
                      <button onClick={() => handleOpenModalForEdit(record)}>Sửa</button>
                      <button onClick={() => handleDelete(record.id)}>Xóa</button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* Pagination */}
            <div className={styles.pagination}>
              <span>Trang {currentPage} / {totalPages} (Tổng: {totalRecords} bản ghi)</span>
              <div>
                <button 
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Trước
                </button>
                <button 
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Sau
                </button>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Modal */}
      <DowntimeFormModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleFormSubmit}
        initialData={editingRecord}
        isEditMode={isEditMode}
        isLoading={isSubmittingForm}
        cncList={cncList}
      />

      {/* Password Modal */}
      <PasswordModal
        isOpen={showPasswordModal}
        onClose={handleAuthCancel}
        onSuccess={handleAuthSuccess}
        title={modalProps.title}
        message={modalProps.message}
      />
    </div>
  );
};

export default DowntimeNoOrderPage;
