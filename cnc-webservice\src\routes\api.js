const express = require('express');
const router = express.Router();

const cncInforRoutes = require('./cncInforRoutes');
const cncLogsRoutes = require('./cncLogsRoutes');
const stopReasonsRoutes = require('./stopReasonsRoutes');
const cncDowntimeNoOrderRoutes = require('./cncDowntimeNoOrder');
const userActionLogRoutes = require('./userActionLogRoutes');

router.use('/cnc-infor', cncInforRoutes);
router.use('/cnc-logs', cncLogsRoutes);
router.use('/stop-reasons', stopReasonsRoutes);
router.use('/cnc-downtime-no-order', cncDowntimeNoOrderRoutes);
router.use('/user-action-logs', userActionLogRoutes);

// Thêm một route cơ bản cho /api để kiểm tra
router.get('/', (req, res) => {
  res.send('API is working!');
});

module.exports = router; 