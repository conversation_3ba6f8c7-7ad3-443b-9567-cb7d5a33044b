/* src/pages/DowntimeNoOrderPage.module.scss */
.pageContainer {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: nowrap;
  gap: 20px;

  h1 {
    margin: 0;
    color: var(--text-primary-color, #333);
    white-space: nowrap;
    font-size: 1.8rem;
    flex-shrink: 0;
  }
}

.addButton {
  padding: 10px 20px;
  background-color: var(--button-primary-bg, #007bff);
  color: var(--button-text-color, #fff);
  border: none;
  border-radius: var(--border-radius-sm, 4px);
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--button-primary-hover-bg, #0056b3);
  }
}

.filtersContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--card-bg-color, #fff);
  border-radius: var(--border-radius-md, 8px);
  box-shadow: var(--card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1));

  div {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary-color, #555);
  }

  input[type="date"],
  select {
    padding: 8px 10px;
    border: 1px solid var(--input-border-color, #ccc);
    border-radius: var(--border-radius-sm, 4px);
    font-size: 0.95rem;
    min-width: 150px;
  }

  button {
    padding: 10px 20px;
    background-color: var(--button-secondary-bg, #6c757d);
    color: var(--button-text-color, #fff);
    border: none;
    border-radius: var(--border-radius-sm, 4px);
    cursor: pointer;
    font-weight: 500;
    align-self: flex-end;

    &:hover {
      background-color: var(--button-secondary-hover-bg, #545b62);
    }
  }
}

.error {
  padding: 10px;
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: var(--border-radius-sm, 4px);
  color: #dc3545;
  margin-bottom: 20px;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: var(--card-bg-color, #fff);
  padding: 30px;
  border-radius: var(--border-radius-md, 8px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;

  h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--text-primary-color, #333);
  }

  form {
    display: flex;
    flex-direction: column;
    gap: 15px;

    div {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    label {
      font-weight: 500;
      color: var(--text-secondary-color, #555);
    }

    input,
    select,
    textarea {
      padding: 10px;
      border: 1px solid var(--input-border-color, #ccc);
      border-radius: var(--border-radius-sm, 4px);
      font-size: 1rem;

      &:focus {
        outline: none;
        border-color: var(--button-primary-bg, #007bff);
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }
    }

    small {
      color: var(--text-muted-color, #6c757d);
      font-size: 0.85rem;
    }
  }
}

.formActions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;

  button {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius-sm, 4px);
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;

    &[type="submit"] {
      background-color: var(--button-primary-bg, #007bff);
      color: var(--button-text-color, #fff);

      &:hover {
        background-color: var(--button-primary-hover-bg, #0056b3);
      }
    }

    &[type="button"] {
      background-color: var(--button-secondary-bg, #6c757d);
      color: var(--button-text-color, #fff);

      &:hover {
        background-color: var(--button-secondary-hover-bg, #545b62);
      }
    }
  }
}

.tableContainer {
  background-color: var(--card-bg-color, #fff);
  border-radius: var(--border-radius-md, 8px);
  box-shadow: var(--card-shadow, 0 2px 4px rgba(0, 0, 0, 0.1));
  overflow: hidden;
}

.loading {
  text-align: center;
  padding: 40px;
  color: var(--text-secondary-color, #555);
  font-size: 1.1rem;
}

.table {
  width: 100%;
  border-collapse: collapse;

  th,
  td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color, #dee2e6);
  }

  th {
    background-color: var(--table-header-bg, #f8f9fa);
    font-weight: 600;
    color: var(--text-primary-color, #333);
  }

  tbody tr:hover {
    background-color: var(--table-row-hover-bg, #f5f5f5);
  }

  td button {
    padding: 5px 10px;
    margin-right: 5px;
    border: none;
    border-radius: var(--border-radius-sm, 4px);
    cursor: pointer;
    font-size: 0.85rem;
    transition: background-color 0.2s ease;

    &:first-of-type {
      background-color: var(--button-warning-bg, #ffc107);
      color: var(--button-warning-text, #212529);

      &:hover {
        background-color: var(--button-warning-hover-bg, #e0a800);
      }
    }

    &:last-of-type {
      background-color: var(--button-danger-bg, #dc3545);
      color: var(--button-text-color, #fff);

      &:hover {
        background-color: var(--button-danger-hover-bg, #c82333);
      }
    }
  }
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: var(--table-footer-bg, #f8f9fa);

  span {
    color: var(--text-secondary-color, #555);
    font-size: 0.9rem;
  }

  div {
    display: flex;
    gap: 10px;
  }

  button {
    padding: 8px 15px;
    background-color: var(--button-secondary-bg, #6c757d);
    color: var(--button-text-color, #fff);
    border: none;
    border-radius: var(--border-radius-sm, 4px);
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;

    &:hover:not(:disabled) {
      background-color: var(--button-secondary-hover-bg, #545b62);
    }

    &:disabled {
      background-color: var(--button-disabled-bg, #ccc);
      cursor: not-allowed;
    }
  }
}
