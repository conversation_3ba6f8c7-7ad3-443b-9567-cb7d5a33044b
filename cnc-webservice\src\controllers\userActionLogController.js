const UserActionLog = require('../models/UserActionLog');

// GET /user-action-logs - L<PERSON><PERSON> danh sách logs
const getUserActionLogs = (req, res) => {
  const params = req.query;
  
  UserActionLog.getAll(params, (err, result) => {
    if (err) {
      console.error('Error fetching user action logs:', err);
      res.status(500).json({
        success: false,
        message: 'Lỗi khi lấy danh sách lịch sử thao tác',
        error: err.message
      });
    } else {
      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    }
  });
};

// POST /user-action-logs - Tạo log mới
const createUserActionLog = (req, res) => {
  const { userId, actionType, tableName, recordId, recordDescription, actionDetails } = req.body;

  // Validation
  if (!userId || !actionType || !tableName || !recordId || !recordDescription) {
    return res.status(400).json({
      success: false,
      message: 'Thiếu thông tin bắt buộc: userId, actionType, tableName, recordId, recordDescription'
    });
  }

  const validActionTypes = ['CREATE', 'UPDATE', 'DELETE'];
  if (!validActionTypes.includes(actionType)) {
    return res.status(400).json({
      success: false,
      message: 'actionType phải là một trong: CREATE, UPDATE, DELETE'
    });
  }

  UserActionLog.create({
    userId,
    actionType,
    tableName,
    recordId,
    recordDescription,
    actionDetails
  }, (err, createdLog) => {
    if (err) {
      console.error('Error creating user action log:', err);
      res.status(500).json({
        success: false,
        message: 'Lỗi khi tạo log thao tác',
        error: err.message
      });
    } else {
      res.status(201).json({
        success: true,
        message: 'Tạo log thao tác thành công',
        data: createdLog
      });
    }
  });
};

// DELETE /user-action-logs/:id - Xóa log (nếu cần)
const deleteUserActionLog = (req, res) => {
  const { id } = req.params;

  UserActionLog.delete(id, (err, result) => {
    if (err) {
      if (err.message === 'Record not found') {
        return res.status(404).json({
          success: false,
          message: 'Không tìm thấy log'
        });
      }
      
      console.error('Error deleting user action log:', err);
      res.status(500).json({
        success: false,
        message: 'Lỗi khi xóa log',
        error: err.message
      });
    } else {
      res.json({
        success: true,
        message: 'Xóa log thành công'
      });
    }
  });
};

// GET /user-action-logs/stats/users - Thống kê theo user
const getUserStats = (req, res) => {
  UserActionLog.getStatsByUser((err, stats) => {
    if (err) {
      console.error('Error fetching user stats:', err);
      res.status(500).json({
        success: false,
        message: 'Lỗi khi lấy thống kê theo user',
        error: err.message
      });
    } else {
      res.json({
        success: true,
        data: stats
      });
    }
  });
};

// GET /user-action-logs/stats/tables - Thống kê theo bảng
const getTableStats = (req, res) => {
  UserActionLog.getStatsByTable((err, stats) => {
    if (err) {
      console.error('Error fetching table stats:', err);
      res.status(500).json({
        success: false,
        message: 'Lỗi khi lấy thống kê theo bảng',
        error: err.message
      });
    } else {
      res.json({
        success: true,
        data: stats
      });
    }
  });
};

module.exports = {
  getUserActionLogs,
  createUserActionLog,
  deleteUserActionLog,
  getUserStats,
  getTableStats
};
