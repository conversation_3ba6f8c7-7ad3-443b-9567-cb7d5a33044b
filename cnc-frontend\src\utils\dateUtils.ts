/**
 * Date utility functions for CNC application
 */

// Ng<PERSON>y bắt đầu cho phép chọn (có thể thay đổi dễ dàng)
export const MIN_ALLOWED_DATE = '2025-04-01';

/**
 * Lấy ngày hôm nay theo định dạng YYYY-MM-DD
 */
export const getTodayString = (): string => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

/**
 * Kiểm tra xem một ngày có hợp lệ để chọn không
 * @param dateString - Ngày cần kiểm tra (YYYY-MM-DD)
 * @returns true nếu ngày hợp lệ, false nếu không
 */
export const isDateSelectable = (dateString: string): boolean => {
  const date = new Date(dateString);
  const minDate = new Date(MIN_ALLOWED_DATE);
  const today = new Date(getTodayString());
  
  // Ngày phải >= MIN_ALLOWED_DATE và <= hôm nay
  return date >= minDate && date <= today;
};

/**
 * Lấy ngày tối thiểu có thể chọn
 */
export const getMinSelectableDate = (): string => {
  return MIN_ALLOWED_DATE;
};

/**
 * Lấy ngày tối đa có thể chọn (hôm nay)
 */
export const getMaxSelectableDate = (): string => {
  return getTodayString();
};

/**
 * Lấy ngày tối đa có thể chọn cho downtime records (1 tuần tới)
 */
export const getMaxSelectableDateForDowntime = (): string => {
  const today = new Date();
  const oneWeekLater = new Date(today);
  oneWeekLater.setDate(today.getDate() + 7);
  return oneWeekLater.toISOString().split('T')[0];
};

/**
 * Validate và điều chỉnh ngày nếu cần thiết
 * @param dateString - Ngày cần validate
 * @returns Ngày đã được điều chỉnh nếu cần
 */
export const validateAndAdjustDate = (dateString: string): string => {
  const minDate = getMinSelectableDate();
  const maxDate = getMaxSelectableDate();
  
  if (dateString < minDate) {
    return minDate;
  }
  
  if (dateString > maxDate) {
    return maxDate;
  }
  
  return dateString;
};

/**
 * Lấy ngày mặc định cho startDate (7 ngày trước hôm nay hoặc MIN_ALLOWED_DATE)
 */
export const getDefaultStartDate = (): string => {
  const today = new Date();
  const sevenDaysAgo = new Date(today);
  sevenDaysAgo.setDate(today.getDate() - 7);
  
  const sevenDaysAgoString = sevenDaysAgo.toISOString().split('T')[0];
  const minDate = getMinSelectableDate();
  
  // Trả về ngày lớn hơn giữa 7 ngày trước và MIN_ALLOWED_DATE
  return sevenDaysAgoString >= minDate ? sevenDaysAgoString : minDate;
};

/**
 * Lấy ngày mặc định cho endDate (hôm nay)
 */
export const getDefaultEndDate = (): string => {
  return getTodayString();
};
