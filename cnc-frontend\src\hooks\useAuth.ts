import { useState, useCallback } from 'react';

export interface AuthState {
  isAuthenticated: boolean;
  userRole: string | null;
  timestamp: number | null;
}

export interface UseAuthReturn {
  authState: AuthState;
  showPasswordModal: boolean;
  pendingAction: (() => void) | null;
  requestAuth: (action: () => void, title?: string, message?: string) => void;
  handleAuthSuccess: (userRole: string) => void;
  handleAuthCancel: () => void;
  logout: () => void;
  modalProps: {
    title?: string;
    message?: string;
  };
}

// Session timeout: 30 minutes
const SESSION_TIMEOUT = 30 * 60 * 1000;

export const useAuth = (): UseAuthReturn => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    userRole: null,
    timestamp: null
  });
  
  const [showPasswordModal, setShowPasswordModal] = useState<boolean>(false);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);
  const [modalProps, setModalProps] = useState<{ title?: string; message?: string }>({});

  const isSessionValid = useCallback((): boolean => {
    if (!authState.isAuthenticated || !authState.timestamp) {
      return false;
    }
    
    const now = Date.now();
    return (now - authState.timestamp) < SESSION_TIMEOUT;
  }, [authState]);

  const requestAuth = useCallback((
    action: () => void, 
    title?: string, 
    message?: string
  ) => {
    // Check if already authenticated and session is valid
    if (authState.isAuthenticated && isSessionValid()) {
      // Execute action immediately
      action();
      return;
    }

    // Need authentication
    setPendingAction(() => action);
    setModalProps({ title, message });
    setShowPasswordModal(true);
  }, [authState.isAuthenticated, isSessionValid]);

  const handleAuthSuccess = useCallback((userRole: string) => {
    setAuthState({
      isAuthenticated: true,
      userRole,
      timestamp: Date.now()
    });
    
    setShowPasswordModal(false);
    
    // Execute pending action
    if (pendingAction) {
      pendingAction();
      setPendingAction(null);
    }
    
    setModalProps({});
  }, [pendingAction]);

  const handleAuthCancel = useCallback(() => {
    setShowPasswordModal(false);
    setPendingAction(null);
    setModalProps({});
  }, []);

  const logout = useCallback(() => {
    setAuthState({
      isAuthenticated: false,
      userRole: null,
      timestamp: null
    });
  }, []);

  return {
    authState,
    showPasswordModal,
    pendingAction,
    requestAuth,
    handleAuthSuccess,
    handleAuthCancel,
    logout,
    modalProps
  };
};
