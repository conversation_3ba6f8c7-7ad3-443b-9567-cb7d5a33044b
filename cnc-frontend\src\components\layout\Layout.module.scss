.appContainer {
  display: flex;
  width: 100%;
  min-height: 100vh;
  flex-direction: row;
}

.header {
  width: 100%;
  padding: 0.5rem 1rem;
  text-align: center;
  flex-shrink: 0;
}

.headerContainerSticky {
  // position: sticky;
  // top: 0;
  display : flex;
  z-index: 900;
  width: 100%;
  max-height: 20vh;
}

.liquid-border {
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: linear-gradient(135deg, rgba(170, 221, 238, 0.2), rgba(163, 102, 102, 0.05));
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.liquid-border:hover {
  border: 1px solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  backdrop-filter: blur(20px);
  background: rgba(201, 201, 201, 0.3);
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  z-index: 1000;
  transition: width 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-x: hidden;
}

.sidebarExpanded {
  width: 250px;
}

.sidebarCollapsed {
  width: 60px;
}

.sidebarCollapsed .navText {
  display: none;
}

.sidebarCollapsed .sidebarIcon {
  margin-right: 0;

}

.sidebarCollapsed nav ul li a {
  justify-content: center;
  padding-left: 0;
  padding-right: 0;
}

.sidebar nav {
  flex-grow: 1;
}

.sidebar nav ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.sidebar nav ul li a {
  text-decoration: none;
  color: #333;
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  white-space: nowrap;
  transition: background 0.3s ease, box-shadow 0.3s ease, transform 0.3s ease;
}

.sidebar nav ul li a .sidebarIcon {
  font-size: 1.2rem;
}

.sidebar nav ul li a:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.25),
    rgba(255, 255, 255, 0.1)
  );
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: scale(1.03);
}

.contentWrapper {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding-left: 60px;
}

.pageContent {
  flex-grow: 1;
  padding: 1rem;
}

.footer {
  background-color: #282c34;
  padding: 1rem;
  color: white;
  text-align: center;
  flex-shrink: 0;
}

.logoContainer {
  padding: 0.5rem;
  text-align: center;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.logoIcon {
  max-width: 40px;
  max-height: 40px;
  display: block;
}

.logoFull {
  max-height: 45px;
  display: block;
  opacity: 0;
  transform: translateX(-100%);
  transition: opacity 0.3s ease-out 0.1s, transform 0.3s ease-out 0.1s;
}

.sidebarExpanded {
  .logoFull {
    opacity: 1;
    transform: translateX(0);
  }
  .navText {
    margin-left : 0.75rem
  }
}

.sidebarCollapsed .logoFull {
  display: none;
}

.sidebarExpanded .logoIcon {
  display: none;
} 