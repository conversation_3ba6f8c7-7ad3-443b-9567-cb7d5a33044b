import React, { useState, useEffect } from 'react';
import type { FormEvent } from 'react';
import styles from './CncFormModal.module.scss'; // Sử dụng chung CSS

interface PasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (userRole: string) => void;
  title?: string;
  message?: string;
}

// Định nghĩa các role và mật khẩu
const PASSWORD_ROLES = {
  'admin123': 'Admin',
  'manager456': 'Manager', 
  'operator789': 'Operator',
  'supervisor999': 'Supervisor'
};

const PasswordModal: React.FC<PasswordModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  title = '<PERSON>á<PERSON> thực quyền truy cập',
  message = 'Vui lòng nhập mật khẩu để thực hiện thao tác này:'
}) => {
  const [password, setPassword] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (isOpen) {
      setPassword('');
      setError('');
      setIsLoading(false);
    }
  }, [isOpen]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Simulate a small delay for better UX
    setTimeout(() => {
      const userRole = PASSWORD_ROLES[password as keyof typeof PASSWORD_ROLES];
      
      if (userRole) {
        onSuccess(userRole);
        setPassword('');
        setError('');
      } else {
        setError('Mật khẩu không đúng. Vui lòng thử lại.');
      }
      setIsLoading(false);
    }, 500);
  };

  const handleClose = () => {
    setPassword('');
    setError('');
    setIsLoading(false);
    onClose();
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className={styles.modalOverlay} onClick={handleClose}>
      <div className={isOpen ? styles.modalContent : styles.modalContentHidden} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h2>{title}</h2>
          <button onClick={handleClose} className={styles.closeButton} aria-label="Đóng modal">×</button>
        </div>
        <form onSubmit={handleSubmit} className={styles.modalBody}>
          <p style={{ 
            color: 'var(--modal-text-secondary)', 
            marginBottom: 'var(--modal-spacing-lg)',
            lineHeight: 1.6 
          }}>
            {message}
          </p>

          <div className={styles.formGroup}>
            <label htmlFor="password">Mật khẩu <span className={styles.required}>*</span></label>
            <input
              type="password"
              id="password"
              name="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Nhập mật khẩu..."
              className={error ? styles.inputError : ''}
              autoFocus
              disabled={isLoading}
            />
            {error && <p className={styles.errorMessage}>{error}</p>}
          </div>



          <div className={styles.modalFooter}>
            <button 
              type="button" 
              onClick={handleClose}
              disabled={isLoading}
              style={{
                padding: 'var(--modal-spacing-sm) var(--modal-spacing-lg)',
                border: '1px solid var(--modal-input-border)',
                borderRadius: 'var(--modal-border-radius-md)',
                backgroundColor: 'transparent',
                color: 'var(--modal-text-secondary)',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                marginRight: 'var(--modal-spacing-md)'
              }}
            >
              Hủy
            </button>
            <button type="submit" className={styles.submitButton} disabled={isLoading || !password.trim()}>
              {isLoading ? 'Đang xác thực...' : 'Xác nhận'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PasswordModal;
