# Dự án Giám sát Hiệu suất Thiết bị OEE

Dự án này nhằm xây dựng một hệ thống giám sát hiệu suất tổng thể của thiết bị (OEE) cho các máy CNC, bao gồm hai thành phần chính: một ứng dụng giám sát phía người dùng và một webservice backend.

## Cấu trúc thư mục

```
Oee/
├── CNC_Monitoring/     # Dự án ứng dụng client (C# .NET MAUI - giả định)
│   ├── .vs/
│   ├── bin/
│   ├── Converters/
│   ├── Data/
│   ├── Models/
│   │   └── Network/
│   ├── obj/
│   ├── Services/
│   ├── Utils/
│   ├── ViewModel/
│   └── Views/
├── cnc-webservice/     # Dự án webservice (Node.js)
│   ├── data/           # Chứa file database SQLite
│   ├── scripts/
│   ├── src/
│   │   ├── controllers/
│   │   ├── database/   # Logic tương tác với database
│   │   ├── models/     # Định nghĩa schema cho các bảng database
│   │   ├── routes/     # Định nghĩa các API endpoint
│   │   ├── services/   # Business logic
│   │   ├── tcp/        # Logic giao tiếp TCP/IP với máy CNC
│   │   └── utils/
│   ├── package.json
│   └── server.js       # Điểm khởi chạy webservice
└── serviceLogs/        # Thư mục chứa logs của webservice
```

## Thành phần chính

### 1. `cnc-webservice` (Node.js)

Đây là backend service, chịu trách nhiệm:

*   **Giao tiếp với máy CNC:**
    *   Sử dụng module TCP client (`src/tcp/tcpClient.js`) để kết nối, gửi lệnh và nhận phản hồi từ các máy CNC.
    *   Xử lý và lưu trữ dữ liệu trạng thái, log hoạt động từ máy CNC.
*   **Quản lý dữ liệu:**
    *   Sử dụng **SQLite** làm cơ sở dữ liệu, được lưu trữ trong thư mục `data/`.
    *   Các models dữ liệu chính bao gồm:
        *   `CncInfor`: Thông tin chung về các máy CNC.
        *   `CncLogs`: Nhật ký hoạt động và trạng thái của máy CNC.
        *   `StopReasons`: Danh sách các lý do dừng máy.
    *   Cung cấp các thao tác CRUD (Create, Read, Update, Delete) cho các models này.
*   **Cung cấp API:**
    *   Xây dựng các API endpoint sử dụng Express.js để cho phép ứng dụng client truy xuất và cập nhật dữ liệu.
    *   Sử dụng các thư viện: `express`, `sqlite3`, `cors`, `body-parser`, `dotenv`, `nodemon`.
*   **Logging:**
    *   Ghi log hoạt động của service vào thư mục `serviceLogs/`.

### 2. `CNC_Monitoring` (C# .NET MAUI - giả định)

Đây là ứng dụng client, có thể được phát triển bằng C# .NET MAUI (dựa trên cấu trúc thư mục). Mục đích chính của ứng dụng này là:

*   **Hiển thị thông tin giám sát:**
    *   Kết nối đến `cnc-webservice` để lấy dữ liệu về trạng thái máy CNC, OEE, lịch sử hoạt động, v.v.
    *   Cung cấp giao diện người dùng trực quan để theo dõi và phân tích hiệu suất máy móc.
*   **Tương tác người dùng:**
    *   Cho phép người dùng thực hiện các thao tác như xem chi tiết log, cấu hình máy, v.v. (tùy theo thiết kế).

## Mục tiêu dự án

*   Xây dựng một hệ thống thu thập dữ liệu tự động từ các máy CNC.
*   Tính toán và hiển thị các chỉ số OEE (Availability, Performance, Quality).
*   Cung cấp công cụ để phân tích nguyên nhân dừng máy và các vấn đề về hiệu suất.
*   Hỗ trợ việc ra quyết định nhằm cải thiện hiệu quả sản xuất.

## Các công nghệ chính

*   **Backend (Webservice):**
    *   Node.js
    *   Express.js
    *   SQLite3
    *   TCP/IP Sockets
*   **Frontend (Ứng dụng giám sát - giả định):**
    *   C#
    *   .NET MAUI (hoặc một framework .NET UI khác)

## Thiết lập và Chạy dự án

### `cnc-webservice`

1.  **Cài đặt dependencies:**
    ```bash
    cd cnc-webservice
    npm install
    ```
2.  **Cấu hình môi trường:**
    *   Tạo file `.env` trong thư mục `cnc-webservice` dựa trên file `.env.example` (nếu có) để cấu hình các thông số như port, thông tin kết nối database (nếu cần tách biệt), địa chỉ IP và port của các máy CNC.
3.  **Khởi chạy database (nếu cần):**
    *   Có thể có một script để khởi tạo schema database ban đầu (ví dụ: `npm run init-db`).
4.  **Chạy service:**
    ```bash
    npm start
    ```
    Hoặc ở chế độ development với `nodemon`:
    ```bash
    npm run dev
    ```

### `CNC_Monitoring`

*   (Cần thêm hướng dẫn cụ thể tùy theo công nghệ được sử dụng, ví dụ: mở project bằng Visual Studio, build và run.)

## Các tính năng đã triển khai (trong `cnc-webservice`)

*   Thiết lập project Node.js với Express.
*   Kết nối và thực hiện các thao tác CRUD với database SQLite cho các bảng `CncInfor`, `CncLogs`, `StopReasons`.
*   Module TCP client (`tcpClient.js`) có khả năng:
    *   Kết nối đến nhiều máy CNC.
    *   Gửi lệnh định kỳ.
    *   Nhận và xử lý phản hồi.
    *   Ghi log trạng thái máy vào database.
    *   Xử lý lỗi kết nối và tự động kết nối lại.
    *   Cơ chế logging trạng thái để tránh ghi đè dữ liệu trùng lặp.

## Cập nhật mới nhất

### 🎯 **Tính năng Quản lý Thời gian Dừng Không có Đơn hàng** (Tháng 6/2025)

**Frontend (cnc-frontend):**
- ✅ Trang mới `/downtime-no-order` với đầy đủ CRUD operations
- ✅ Modal form theo design system hiện có với validation đầy đủ
- ✅ Date picker với constraints (không cho phép tương lai và quá khứ xa)
- ✅ Pagination và filtering theo máy CNC và khoảng thời gian
- ✅ Responsive design với sidebar mở rộng 250px

**Backend (cnc-webservice):**
- ✅ Model `CncDowntimeNoOrder` với SQLite thuần (không dùng Sequelize)
- ✅ API endpoints: GET, POST, PUT, DELETE với validation nghiệp vụ
- ✅ Business rules: max 24h/ngày, không trùng lặp (cùng máy + cùng ngày)
- ✅ Database table tự động tạo khi khởi động

**OEE Calculation Enhancement:**
- ✅ Cập nhật `statisticsUtils.ts` với interface `OEECalculationData`
- ✅ Functions tính OEE có tính đến downtime no order:
  - `calculateAvailability()` - Tính Availability
  - `calculatePerformance()` - Tính Performance
  - `calculateQuality()` - Tính Quality
  - `calculateOEE()` - Tính OEE tổng thể

### 🔐 **Hệ thống Authentication & Authorization** (Tháng 6/2025)

**Password-based Role System:**
- ✅ Component `PasswordModal` với UI đẹp và animation
- ✅ Hook `useAuth` quản lý session 30 phút tự động hết hạn
- ✅ 4 roles: Admin, Manager, Operator, Supervisor
- ✅ User tracking: Lưu thông tin người thao tác vào database
- ✅ Toast notifications hiển thị role khi thành công

**Security Features:**
- ✅ Session management với timeout
- ✅ Role-based access control
- ✅ UI indicators hiển thị trạng thái đăng nhập
- ✅ Audit trail sẵn sàng cho lịch sử thao tác

**Applied to Pages:**
- ✅ Downtime No Order page: Thêm/sửa/xóa yêu cầu authentication
- 🔄 Management page: Đang áp dụng
- 🔄 OEE page: Đang áp dụng

### 🎨 **UI/UX Improvements** (Tháng 6/2025)

**Sidebar Enhancements:**
- ✅ Tăng width từ 200px → 250px để hiển thị đầy đủ nội dung
- ✅ Menu item "Thời gian dừng" (rút gọn từ "Dừng không có đơn hàng")

**Content Layout:**
- ✅ Tăng max-width từ 1200px → 1400px
- ✅ Header layout cải thiện với `white-space: nowrap`
- ✅ Button labels ngắn gọn hơn ("Thêm mới" thay vì "Thêm bản ghi mới")

**Modal Design System:**
- ✅ Unified modal design với animation fade in/out
- ✅ Form validation với error states
- ✅ Consistent styling với CSS variables
- ✅ Responsive và accessible

## Các vấn đề cần cải thiện/phát triển tiếp

### 🔄 **Đang triển khai:**
- User Action Log page để xem lịch sử thao tác
- Áp dụng authentication cho Management và OEE pages
- Mở rộng date range cho downtime records (1 tuần tới)

### 📋 **Kế hoạch tiếp theo:**
- Dashboard real-time với WebSocket
- Export/Import data functionality
- Advanced OEE analytics và reporting
- Mobile responsive optimization
- Multi-language support (EN/VI)

### 🛠 **Technical Debt:**
- Migrate từ SQLite thuần sang ORM (TypeORM/Prisma)
- API documentation với Swagger
- Unit tests cho business logic
- Performance optimization cho large datasets