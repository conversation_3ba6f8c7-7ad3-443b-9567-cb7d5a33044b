const CncDowntimeNoOrder = require('../models/CncDowntimeNoOrder');
const CncInfor = require('../models/CncInfor');

// GET /cnc-downtime-no-order - L<PERSON>y danh sách thời gian dừng không có đơn hàng
const getDowntimeNoOrders = (req, res) => {
  const params = req.query;

  CncDowntimeNoOrder.getAll(params, (err, result) => {
    if (err) {
      console.error('Error fetching downtime no orders:', err);
      res.status(500).json({
        success: false,
        message: 'Lỗi khi lấy danh sách thời gian dừng không có đơn hàng',
        error: err.message
      });
    } else {
      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    }
  });
};

// POST /cnc-downtime-no-order - <PERSON><PERSON>o bản ghi mới
const createDowntimeNoOrder = (req, res) => {
  const { cncId, downtimeDate, downtimeMinutes, notes, createdBy } = req.body;

  // Validation
  if (!cncId || !downtimeDate || downtimeMinutes === undefined) {
    return res.status(400).json({
      success: false,
      message: 'Thiếu thông tin bắt buộc: cncId, downtimeDate, downtimeMinutes'
    });
  }

  if (downtimeMinutes < 0 || downtimeMinutes > 1440) {
    return res.status(400).json({
      success: false,
      message: 'Thời gian dừng phải từ 0 đến 1440 phút (24 giờ)'
    });
  }

  // Check if CNC exists
  CncInfor.getById(cncId, (err, cncExists) => {
    if (err) {
      return res.status(500).json({
        success: false,
        message: 'Lỗi khi kiểm tra máy CNC',
        error: err.message
      });
    }

    if (!cncExists) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy máy CNC với ID đã cho'
      });
    }

    // Check for duplicate
    CncDowntimeNoOrder.checkDuplicate(cncId, downtimeDate, null, (err, isDuplicate) => {
      if (err) {
        return res.status(500).json({
          success: false,
          message: 'Lỗi khi kiểm tra trùng lặp',
          error: err.message
        });
      }

      if (isDuplicate) {
        return res.status(409).json({
          success: false,
          message: 'Đã tồn tại bản ghi cho máy này trong ngày đã chọn'
        });
      }

      // Create new record
      CncDowntimeNoOrder.create({
        cncId,
        downtimeDate,
        downtimeMinutes,
        notes,
        createdBy
      }, (err, createdRecord) => {
        if (err) {
          console.error('Error creating downtime no order:', err);
          res.status(500).json({
            success: false,
            message: 'Lỗi khi tạo bản ghi',
            error: err.message
          });
        } else {
          res.status(201).json({
            success: true,
            message: 'Tạo bản ghi thành công',
            data: createdRecord
          });
        }
      });
    });
  });
};

// PUT /cnc-downtime-no-order/:id - Cập nhật bản ghi
const updateDowntimeNoOrder = (req, res) => {
  const { id } = req.params;
  const { cncId, downtimeDate, downtimeMinutes, notes, createdBy } = req.body;

  // Validation
  if (downtimeMinutes !== undefined && (downtimeMinutes < 0 || downtimeMinutes > 1440)) {
    return res.status(400).json({
      success: false,
      message: 'Thời gian dừng phải từ 0 đến 1440 phút (24 giờ)'
    });
  }

  // Get existing record first
  CncDowntimeNoOrder.getById(id, (err, record) => {
    if (err) {
      return res.status(500).json({
        success: false,
        message: 'Lỗi khi tìm bản ghi',
        error: err.message
      });
    }

    if (!record) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy bản ghi'
      });
    }

    // Check for duplicate if cncId or downtimeDate is being changed
    const newCncId = cncId || record.cncId;
    const newDowntimeDate = downtimeDate || record.downtimeDate;

    if ((cncId && cncId !== record.cncId) || (downtimeDate && downtimeDate !== record.downtimeDate)) {
      CncDowntimeNoOrder.checkDuplicate(newCncId, newDowntimeDate, id, (err, isDuplicate) => {
        if (err) {
          return res.status(500).json({
            success: false,
            message: 'Lỗi khi kiểm tra trùng lặp',
            error: err.message
          });
        }

        if (isDuplicate) {
          return res.status(409).json({
            success: false,
            message: 'Đã tồn tại bản ghi cho máy này trong ngày đã chọn'
          });
        }

        // Proceed with update
        performUpdate();
      });
    } else {
      // No need to check duplicate, proceed with update
      performUpdate();
    }

    function performUpdate() {
      const updateData = {
        cncId: newCncId,
        downtimeDate: newDowntimeDate,
        downtimeMinutes: downtimeMinutes !== undefined ? downtimeMinutes : record.downtimeMinutes,
        notes: notes !== undefined ? notes : record.notes,
        createdBy: createdBy || record.createdBy
      };

      CncDowntimeNoOrder.update(id, updateData, (err, updatedRecord) => {
        if (err) {
          console.error('Error updating downtime no order:', err);
          res.status(500).json({
            success: false,
            message: 'Lỗi khi cập nhật bản ghi',
            error: err.message
          });
        } else {
          res.json({
            success: true,
            message: 'Cập nhật bản ghi thành công',
            data: updatedRecord
          });
        }
      });
    }
  });
};

// DELETE /cnc-downtime-no-order/:id - Xóa bản ghi
const deleteDowntimeNoOrder = (req, res) => {
  const { id } = req.params;

  CncDowntimeNoOrder.delete(id, (err, result) => {
    if (err) {
      if (err.message === 'Record not found') {
        return res.status(404).json({
          success: false,
          message: 'Không tìm thấy bản ghi'
        });
      }

      console.error('Error deleting downtime no order:', err);
      res.status(500).json({
        success: false,
        message: 'Lỗi khi xóa bản ghi',
        error: err.message
      });
    } else {
      res.json({
        success: true,
        message: 'Xóa bản ghi thành công'
      });
    }
  });
};

module.exports = {
  getDowntimeNoOrders,
  createDowntimeNoOrder,
  updateDowntimeNoOrder,
  deleteDowntimeNoOrder
};
