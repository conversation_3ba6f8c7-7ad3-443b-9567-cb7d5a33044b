const db = require('../database/db');

const UserActionLog = {
  // Tạo bảng nếu chưa tồn tại
  createTable: () => {
    return new Promise((resolve, reject) => {
      const sql = `CREATE TABLE IF NOT EXISTS UserActionLog (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        userId TEXT NOT NULL,
        actionType TEXT NOT NULL CHECK (actionType IN ('CREATE', 'UPDATE', 'DELETE')),
        tableName TEXT NOT NULL,
        recordId INTEGER NOT NULL,
        recordDescription TEXT NOT NULL,
        actionDetails TEXT,
        actionDate DATETIME NOT NULL DEFAULT (datetime('now')),
        UNIQUE (userId, actionType, tableName, recordId, actionDate)
      )`;
      
      db.run(sql, (err) => {
        if (err) {
          console.error('Error creating UserActionLog table:', err);
          reject(err);
        } else {
          console.log('UserActionLog table checked/created.');
          resolve();
        }
      });
    });
  },

  // Tạo log mới
  create: (data, callback) => {
    const { userId, actionType, tableName, recordId, recordDescription, actionDetails } = data;
    const sql = `INSERT INTO UserActionLog (userId, actionType, tableName, recordId, recordDescription, actionDetails) 
                 VALUES (?, ?, ?, ?, ?, ?)`;
    
    db.run(sql, [userId, actionType, tableName, recordId, recordDescription, actionDetails], function (err) {
      if (err) {
        callback(err, null);
      } else {
        // Lấy bản ghi vừa tạo
        UserActionLog.getById(this.lastID, callback);
      }
    });
  },

  // Lấy tất cả logs với pagination và filter
  getAll: (params, callback) => {
    const { userId, actionType, tableName, startDate, endDate, page = 1, limit = 50 } = params;
    const offset = (page - 1) * limit;
    
    let whereClause = '';
    let whereParams = [];
    let conditions = [];

    // Filter by userId
    if (userId) {
      conditions.push('userId = ?');
      whereParams.push(userId);
    }

    // Filter by actionType
    if (actionType) {
      conditions.push('actionType = ?');
      whereParams.push(actionType);
    }

    // Filter by tableName
    if (tableName) {
      conditions.push('tableName = ?');
      whereParams.push(tableName);
    }

    // Filter by date range
    if (startDate && endDate) {
      conditions.push('DATE(actionDate) BETWEEN ? AND ?');
      whereParams.push(startDate, endDate);
    } else if (startDate) {
      conditions.push('DATE(actionDate) >= ?');
      whereParams.push(startDate);
    } else if (endDate) {
      conditions.push('DATE(actionDate) <= ?');
      whereParams.push(endDate);
    }

    if (conditions.length > 0) {
      whereClause = 'WHERE ' + conditions.join(' AND ');
    }

    // Count total records
    const countSql = `SELECT COUNT(*) as total FROM UserActionLog ${whereClause}`;

    db.get(countSql, whereParams, (err, countResult) => {
      if (err) {
        callback(err, null);
        return;
      }

      const total = countResult.total;
      const totalPages = Math.ceil(total / limit);

      // Get paginated data
      const dataSql = `SELECT * FROM UserActionLog ${whereClause}
                       ORDER BY actionDate DESC 
                       LIMIT ? OFFSET ?`;

      db.all(dataSql, [...whereParams, limit, offset], (err, rows) => {
        if (err) {
          callback(err, null);
        } else {
          callback(null, {
            data: rows,
            pagination: {
              total,
              page: parseInt(page),
              limit: parseInt(limit),
              totalPages
            }
          });
        }
      });
    });
  },

  // Lấy log theo ID
  getById: (id, callback) => {
    const sql = `SELECT * FROM UserActionLog WHERE id = ?`;
    
    db.get(sql, [id], (err, row) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, row);
      }
    });
  },

  // Xóa log (nếu cần)
  delete: (id, callback) => {
    const sql = `DELETE FROM UserActionLog WHERE id = ?`;
    
    db.run(sql, [id], function (err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Record not found'), null);
      } else {
        callback(null, { success: true, changes: this.changes });
      }
    });
  },

  // Lấy thống kê theo user
  getStatsByUser: (callback) => {
    const sql = `SELECT userId, 
                        COUNT(*) as totalActions,
                        COUNT(CASE WHEN actionType = 'CREATE' THEN 1 END) as createCount,
                        COUNT(CASE WHEN actionType = 'UPDATE' THEN 1 END) as updateCount,
                        COUNT(CASE WHEN actionType = 'DELETE' THEN 1 END) as deleteCount,
                        MAX(actionDate) as lastAction
                 FROM UserActionLog 
                 GROUP BY userId 
                 ORDER BY totalActions DESC`;
    
    db.all(sql, [], (err, rows) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, rows);
      }
    });
  },

  // Lấy thống kê theo bảng
  getStatsByTable: (callback) => {
    const sql = `SELECT tableName, 
                        COUNT(*) as totalActions,
                        COUNT(CASE WHEN actionType = 'CREATE' THEN 1 END) as createCount,
                        COUNT(CASE WHEN actionType = 'UPDATE' THEN 1 END) as updateCount,
                        COUNT(CASE WHEN actionType = 'DELETE' THEN 1 END) as deleteCount,
                        MAX(actionDate) as lastAction
                 FROM UserActionLog 
                 GROUP BY tableName 
                 ORDER BY totalActions DESC`;
    
    db.all(sql, [], (err, rows) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, rows);
      }
    });
  }
};

module.exports = UserActionLog;
