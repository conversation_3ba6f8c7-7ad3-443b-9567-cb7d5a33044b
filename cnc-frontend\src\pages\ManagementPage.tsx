import React, { useEffect, useState, useMemo } from 'react';
import { socketService } from '../services/socketService';
import { AxiosError } from 'axios';
import { toast } from 'react-toastify';
import { getAllCncInfor, createCncInfor, updateCncInfor } from '../services/apiService';
import type { CncInfor, CncWebSocketUpdate, CncInforFormData} from '../types';
import styles from './ManagementPage.module.scss';
import { saveToLocalStorage, loadFromLocalStorage } from '../utils/localStorageUtils';
import { FaPlus, FaEdit } from 'react-icons/fa';
import CncFormModal from '../components/modals/CncFormModal';
import PasswordModal from '../components/modals/PasswordModal';
import { useAuth } from '../hooks/useAuth';

const LOCAL_STORAGE_KEYS = {
  INITIAL_CNC_MACHINES: 'management_initialCncMachines',
  LIVE_CNC_DATA: 'management_liveCncData',
};

interface CncDataMap {
  [id: number]: CncWebSocketUpdate;
}

interface CombinedCncData extends CncInfor {
  liveStatus?: string | null;
  liveMotionTime?: string | null;
  liveUpdateAt?: string | null;
  isConnectedByWebSocket?: boolean;
}

const ManagementPage: React.FC = () => {
  const [initialCncMachines, setInitialCncMachines] = useState<CncInfor[]>(() => {
    const savedData = loadFromLocalStorage(LOCAL_STORAGE_KEYS.INITIAL_CNC_MACHINES, []);
    console.log('[ManagementPage] Loaded initialCncMachines from localStorage:', savedData);
    // Đảm bảo dữ liệu từ localStorage là một mảng
    return Array.isArray(savedData) ? savedData : [];
  });
  const [liveCncData, setLiveCncData] = useState<CncDataMap>(() => {
    const savedData = loadFromLocalStorage(LOCAL_STORAGE_KEYS.LIVE_CNC_DATA, {} as CncDataMap);
    console.log('[ManagementPage] Loaded liveCncData from localStorage:', savedData);
    // Đảm bảo dữ liệu từ localStorage là một object
    return typeof savedData === 'object' && savedData !== null ? savedData as CncDataMap : {} as CncDataMap;
  });

  const [isLoadingApi, setIsLoadingApi] = useState<boolean>(false); // Loading cho fetch danh sách
  const [isSubmittingForm, setIsSubmittingForm] = useState<boolean>(false); // Loading cho submit modal
  // Removed unused socketError state
  const [apiError, setApiError] = useState<string | null>(null);
  const [isSocketConnected, setIsSocketConnected] = useState<boolean>(socketService.getSocketState() === WebSocket.OPEN);

  // Modal states
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [editingCncData, setEditingCncData] = useState<CncInfor | null>(null);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  // Authentication
  const {
    authState,
    showPasswordModal,
    requestAuth,
    handleAuthSuccess,
    handleAuthCancel,
    modalProps
  } = useAuth();

  const fetchInitialData = async () => {
    // Chỉ set loading nếu chưa có data ban đầu, để tránh loading nhấp nháy khi refresh
    if (!initialCncMachines || !Array.isArray(initialCncMachines) || initialCncMachines.length === 0) {
        setIsLoadingApi(true);
    }
    try {
      console.log('[ManagementPage] Fetching CNC information...');
      const machines = await getAllCncInfor();

      // Kiểm tra dữ liệu trả về từ API
      if (!machines || !Array.isArray(machines)) {
        console.error('[ManagementPage] API returned invalid data:', machines);
        throw new Error('API returned invalid data');
      }

      console.log('[ManagementPage] Fetched machines:', machines);
      setInitialCncMachines(machines);
      saveToLocalStorage(LOCAL_STORAGE_KEYS.INITIAL_CNC_MACHINES, machines);
      setApiError(null);
    } catch (err) {
      console.error('[ManagementPage] API fetch error:', err);
      const errorMsg = err instanceof Error ? err.message : 'Failed to fetch CNC information.';
      setApiError(errorMsg); // Hiển thị lỗi API
      // Không clear initialCncMachines để giữ lại dữ liệu cũ nếu có
    } finally {
      setIsLoadingApi(false);
    }
  };

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    function handleCncUpdate(data: any) {
      if (!data) return;

      if (data.type === 'cnc_update' && data.payload) {
        const payload = data.payload as CncWebSocketUpdate;
        setLiveCncData(prevData => {
          const newData = {
            ...prevData,
            [payload.id]: payload,
          };
          saveToLocalStorage(LOCAL_STORAGE_KEYS.LIVE_CNC_DATA, newData);
          return newData;
        });
      }

      else if (data.type === 'initial_state' && Array.isArray(data.payload)) {
        const payload = data.payload as CncWebSocketUpdate[];
        // console.log('[ManagementPage] Initial state received:', payload);
        setLiveCncData(() => {
          const newData = payload.reduce((acc, cnc) => {
            acc[cnc.id] = cnc;
            return acc;
          }, {} as Record<number, CncWebSocketUpdate>);
          saveToLocalStorage(LOCAL_STORAGE_KEYS.LIVE_CNC_DATA, newData);
          return newData;
        });
      }
    }

    const connectAndListen = async () => {
      try {
        if (socketService.getSocketState() !== WebSocket.OPEN) {
          await socketService.connect();
        }
        setIsSocketConnected(true);
        socketService.addMessageListener(handleCncUpdate);
        console.log('[ManagementPage] WebSocket connected and listener added.');
      } catch (err) {
        console.error('[ManagementPage] WebSocket connection failed:', err);
        setIsSocketConnected(false);
      }
    };

    connectAndListen();

    const intervalId = setInterval(() => {
        const currentState = socketService.getSocketState();
        setIsSocketConnected(currentState === WebSocket.OPEN);
    }, 5000);

    return () => {
      socketService.removeMessageListener(handleCncUpdate);
      clearInterval(intervalId);
      // socketService.disconnect(); // Nếu muốn ngắt khi component unmount
      console.log('[ManagementPage] WebSocket listener removed.');
    };
  }, []);

  const mergedCncData: CombinedCncData[] = useMemo(() => {
    console.log('[ManagementPage] useMemo running with:', {
      initialCncMachines: initialCncMachines,
      isArray: Array.isArray(initialCncMachines),
      length: initialCncMachines?.length
    });

    // Kiểm tra initialCncMachines có phải là mảng không
    if (!initialCncMachines || !Array.isArray(initialCncMachines)) {
      console.error('[ManagementPage] initialCncMachines is not an array:', initialCncMachines);
      return []; // Trả về mảng rỗng nếu không phải mảng
    }

    try {
      // Sử dụng try-catch để bắt lỗi khi xử lý dữ liệu
      const result = initialCncMachines.map(machine => {
        if (!machine || typeof machine !== 'object') {
          console.error('[ManagementPage] Invalid machine object:', machine);
          return null; // Bỏ qua các phần tử không hợp lệ
        }

        const liveData = machine.Id ? liveCncData[machine.Id] : null;
        const machineIsConnected = liveData
                                  && liveData.status !== 'CONNECTING'
                                  && liveData.status !== 'CANTCONNECT'
                                  && liveData.status !== 'TIMEOUTRESP'
                                  && liveData.status !== 'DISCONNECTED';

        return {
          ...machine,
          liveStatus: liveData?.status || 'N/A',
          liveMotionTime: liveData?.motionTime || 'N/A',
          liveUpdateAt: liveData ? new Date(liveData.updateAt).toLocaleString() : 'N/A',
          isConnectedByWebSocket: !!machineIsConnected,
        };
      })
      .filter(item => item !== null) // Lọc bỏ các phần tử null
      .sort((a, b) => {
        if (!a || !b) return 0;
        return (a.Name?.localeCompare(b?.Name || '') || 0);
      });

      console.log('[ManagementPage] Processed mergedCncData:', result);
      return result as CombinedCncData[];
    } catch (error) {
      console.error('[ManagementPage] Error processing CNC data:', error);
      return []; // Trả về mảng rỗng nếu có lỗi
    }
  }, [initialCncMachines, liveCncData]);

  const handleOpenModalForCreate = () => {
    requestAuth(() => {
      setIsEditMode(false);
      setEditingCncData(null);
      setIsModalOpen(true);
    }, 'Xác thực để thêm máy CNC', 'Vui lòng nhập mật khẩu để thêm máy CNC mới:');
  };

  const handleOpenModalForEdit = (cnc: CncInfor) => {
    requestAuth(() => {
      setIsEditMode(true);
      setEditingCncData(cnc);
      setIsModalOpen(true);
    }, 'Xác thực để chỉnh sửa máy CNC', `Vui lòng nhập mật khẩu để chỉnh sửa máy ${cnc.Name}:`);
  };

  const handleCloseModal = () => {
    setTimeout(() => {
      setIsModalOpen(false);
      setEditingCncData(null); // Clear data khi đóng
    }
    , 300); // Thời gian này nên tương ứng với animation của modal
    // Không cần reset isEditMode ở đây vì nó sẽ được set lại khi mở modal
  };

  const handleFormSubmit = async (formData: Partial<CncInforFormData>, id?: number) => {
    setIsSubmittingForm(true);
    setApiError(null); // Clear lỗi API cũ
    try {
      if (isEditMode && id !== undefined) {
        // Chế độ chỉnh sửa
        await updateCncInfor(id, formData);
        toast.success(`Cập nhật máy ${formData.Name || id} thành công! (${authState.userRole})`);
      } else {
        // Chế độ thêm mới
        // Đảm bảo formData đủ các trường required cho CncInforFormData
        await createCncInfor(formData as CncInforFormData); // Ép kiểu nếu chắc chắn đủ trường
        toast.success(`Thêm máy ${formData.Name || ''} thành công! (${authState.userRole})`);
      }
      handleCloseModal();
      await fetchInitialData(); // Tải lại danh sách máy
    } catch (err) {
      console.error('[ManagementPage] Form submit error:', err);
      let errorMsg = 'Lỗi không xác định khi gửi form.';

      if (err instanceof AxiosError) {
        errorMsg = err.response?.data?.message || err.message;
      } else if (err instanceof Error) {
        errorMsg = err.message;
      }

      setApiError(errorMsg); // Hiển thị lỗi API ngay trên page hoặc trong modal

      const actionText = isEditMode ? 'cập nhật' : 'thêm mới';
      toast.error(`Lỗi ${actionText} máy CNC: ${errorMsg}`);
    } finally {
      setIsSubmittingForm(false);
    }
  };

  if (isLoadingApi && initialCncMachines.length === 0 && !apiError) { // Chỉ hiển thị loading toàn trang nếu chưa có data và chưa có lỗi
    return <div className={styles.message}>Loading CNC information...</div>;
  }

  if (apiError && initialCncMachines.length === 0) {
    return <div className={`${styles.message} ${styles.error}`}>Error: {apiError}</div>;
  }

  // if (!isLoadingApi && mergedCncData.length === 0 && !apiError) {
  //     return <div className={styles.message}>No CNC machines configured or found.</div>;
  // }

  return (
    <div className={styles.managementPageContainer}>
      {/* Hiển thị lỗi API toàn cục (nếu có và không phải loading ban đầu) */}
      {apiError && !isModalOpen && ( // Chỉ hiển thị lỗi API chung khi modal đóng
          <div className={`${styles.message} ${styles.error} ${styles.globalError}`}>
              Lỗi: {apiError}
          </div>
      )}
      <p className={styles.socketStatus}>
        WebSocket Status:
        <span className={isSocketConnected ? styles.connected : styles.disconnected}>
          {isSocketConnected ? 'Connected' : 'Disconnected'}
        </span>
      </p>
      {isLoadingApi && initialCncMachines.length > 0 && (
        <div className={styles.loadingUpdateMessage}>Đang cập nhật danh sách máy...</div>
      )}
      <div className={styles.cncListContainer}>
        {mergedCncData.map(cnc => (
          <div key={cnc.Id} className={styles.cncCard}>
            <button
              className={styles.editButton}
              onClick={() => handleOpenModalForEdit(cnc)}
              aria-label={`Chỉnh sửa máy ${cnc.Name || cnc.Id}`}
              title="Chỉnh sửa"
            >
              <FaEdit />
            </button>
            <div className={styles.cncHeader}>
                <span
                    className={`${styles.connectionDot} ${cnc.isConnectedByWebSocket ? styles.dotConnected : styles.dotDisconnected}`}
                    title={cnc.isConnectedByWebSocket ? 'Connected to CNC' : 'Disconnected from CNC'}
                ></span>
                <h2 className={styles.cncName}>{cnc.Name || `CNC ID: ${cnc.Id}`}</h2>
            </div>
            <p><strong>IP:</strong> {cnc.IP || 'N/A'} | <strong>Port:</strong> {cnc.Port || 'N/A'}</p>
            <p><strong>Trạng thái làm việc:</strong> <span className={styles.statusValue}>{cnc.liveStatus}</span></p>
            <p><strong>Motion Time:</strong> {cnc.liveMotionTime}</p>
            <p><strong>Last Update:</strong> {cnc.liveUpdateAt}</p>
          </div>
        ))}
        {/* Card "Add New CNC" */}
        <div
          className={`${styles.cncCard} ${styles.addCncCard}`}
          onClick={handleOpenModalForCreate}
          role="button" // Thêm role cho accessibility
          tabIndex={0}  // Cho phép focus bằng bàn phím
          onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') handleOpenModalForCreate(); }} // Xử lý click bằng Enter/Space
        >
          <FaPlus className={styles.addCncIcon} />
          <span className={styles.addCncText}>Thêm máy CNC mới</span>
        </div>
      </div>
      {/* Thông báo nếu không có máy nào và API không lỗi */}
      {!isLoadingApi && mergedCncData.length === 0 && !apiError && (
        <div className={styles.message} style={{marginTop: '20px'}}>
            Không có máy CNC nào được cấu hình. Click vào card "Thêm máy CNC mới" để bắt đầu.
        </div>
      )}
      {/* FormModal */}
      <CncFormModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleFormSubmit}
        initialData={editingCncData}
        isEditMode={isEditMode}
        isLoading={isSubmittingForm}
      />

      {/* Password Modal */}
      <PasswordModal
        isOpen={showPasswordModal}
        onClose={handleAuthCancel}
        onSuccess={handleAuthSuccess}
        title={modalProps.title}
        message={modalProps.message}
      />
    </div>
  );
};

export default ManagementPage;