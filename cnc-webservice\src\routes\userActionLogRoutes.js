const express = require('express');
const router = express.Router();
const {
  getUserActionLogs,
  createUserActionLog,
  deleteUserActionLog,
  getUserStats,
  getTableStats
} = require('../controllers/userActionLogController');

// GET /user-action-logs - <PERSON><PERSON><PERSON> logs với pagination và filter
router.get('/', getUserActionLogs);

// POST /user-action-logs - Tạo log mới
router.post('/', createUserActionLog);

// DELETE /user-action-logs/:id - Xóa log
router.delete('/:id', deleteUserActionLog);

// GET /user-action-logs/stats/users - Thống kê theo user
router.get('/stats/users', getUserStats);

// GET /user-action-logs/stats/tables - Thống kê theo bảng
router.get('/stats/tables', getTableStats);

module.exports = router;
